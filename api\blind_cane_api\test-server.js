// Test simple pour vérifier que le serveur peut démarrer
console.log('🧪 Test de démarrage du serveur...');

try {
  // Vérifier les modules requis
  console.log('📦 Vérification des modules...');
  
  const express = require('express');
  console.log('✅ Express OK');
  
  const cors = require('cors');
  console.log('✅ CORS OK');
  
  const helmet = require('helmet');
  console.log('✅ Helmet OK');
  
  const compression = require('compression');
  console.log('✅ Compression OK');
  
  const morgan = require('morgan');
  console.log('✅ Morgan OK');
  
  const rateLimit = require('express-rate-limit');
  console.log('✅ Rate Limit OK');
  
  require('dotenv').config();
  console.log('✅ Dotenv OK');
  
  // Vérifier les fichiers de routes
  console.log('📁 Vérification des routes...');
  
  const authRoutes = require('./routes/auth');
  console.log('✅ Auth routes OK');
  
  const userRoutes = require('./routes/users');
  console.log('✅ User routes OK');
  
  const alerteRoutes = require('./routes/alertes');
  console.log('✅ Alerte routes OK');
  
  const contactRoutes = require('./routes/contacts');
  console.log('✅ Contact routes OK');
  
  const messageRoutes = require('./routes/messages');
  console.log('✅ Message routes OK');
  
  const appelRoutes = require('./routes/appels');
  console.log('✅ Appel routes OK');
  
  const trajetRoutes = require('./routes/trajets');
  console.log('✅ Trajet routes OK');
  
  const notificationRoutes = require('./routes/notifications');
  console.log('✅ Notification routes OK');
  
  console.log('🎉 Tous les modules et routes sont OK !');
  console.log('🚀 Le serveur devrait pouvoir démarrer sans problème.');
  
} catch (error) {
  console.error('❌ Erreur détectée:', error.message);
  console.error('📍 Stack trace:', error.stack);
  process.exit(1);
}

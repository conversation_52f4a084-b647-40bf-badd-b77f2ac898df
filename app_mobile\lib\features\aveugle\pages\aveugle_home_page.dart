import 'package:flutter/material.dart';
import 'package:canne_connectee/features/communication/pages/appels.dart';
import 'package:canne_connectee/features/communication/pages/messages.dart';
import 'package:canne_connectee/features/auth/services/auth_service.dart';
import 'package:canne_connectee/features/auth/pages/simple_user_type_selection_page.dart';
import 'package:canne_connectee/features/voice/services/voice_command_service.dart';
import 'package:canne_connectee/features/voice/services/text_to_speech_service.dart';
import 'package:canne_connectee/core/services/notification_service.dart';
import 'package:canne_connectee/shared/widgets/home_content_widget.dart';

/// Page d'accueil simplifiée pour les utilisateurs aveugles/malvoyants
/// Contient seulement 3 vues principales : Accueil, Appels, Messages
class AveuglesHomePage extends StatefulWidget {
  const AveuglesHomePage({super.key});

  @override
  State<AveuglesHomePage> createState() => _AveuglesHomePageState();
}

class _AveuglesHomePageState extends State<AveuglesHomePage> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool isListening = false;
  bool isSpeaking = false;

  late AnimationController _controller;
  late Animation<double> _animation;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  final VoiceCommandService _voiceCommandService = VoiceCommandService();
  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();
  final TextToSpeechService _ttsService = TextToSpeechService();

  // Seulement 3 pages principales
  final List<Widget> _pages = [
    const HomeContentWidget(), // Accueil
    const AppelsPage(),        // Appels
    const MessagesPage(),      // Messages
  ];

  final List<String> _pageTitles = [
    'Accueil',
    'Appels', 
    'Messages',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
  }

  void _initializeAnimations() {
    // Animation pour le scale du bouton
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Animation pour l'effet wave
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeOut),
    );
  }

  Future<void> _initializeServices() async {
    // Initialiser les services
    await _initializeVoiceCommands();
    _notificationService.initialize();
    await _initializeTTS();
  }

  Future<void> _initializeVoiceCommands() async {
    await _voiceCommandService.initialize();

    // Écouter les commandes vocales
    _voiceCommandService.commandStream.listen((command) {
      _handleVoiceCommand(command);
    });
  }

  Future<void> _initializeTTS() async {
    try {
      await _ttsService.initialize();
      
      // Message de bienvenue
      Future.delayed(const Duration(seconds: 2), () {
        _ttsService.speak('Interface simplifiée activée. Trois sections disponibles : Accueil, Appels et Messages');
      });
    } catch (e) {
      print('Échec de l\'initialisation du TTS: $e');
    }

    // Écouter l'état du TTS
    _ttsService.speakingStream.listen((speaking) {
      if (mounted) {
        setState(() {
          isSpeaking = speaking;
        });
      }
    });
  }

  Future<void> _toggleListening() async {
    setState(() {
      isListening = !isListening;
    });

    if (isListening) {
      // Démarrer les animations
      _controller.repeat(reverse: true);
      _waveController.repeat();

      // Démarrer l'écoute des commandes vocales
      await _voiceCommandService.startListening();

      // Arrêter automatiquement après 10 secondes
      Future.delayed(const Duration(seconds: 10), () {
        if (isListening) {
          _stopListening();
        }
      });
    } else {
      _stopListening();
    }
  }

  Future<void> _stopListening() async {
    setState(() {
      isListening = false;
    });

    // Arrêter les animations
    _controller.stop();
    _controller.reset();
    _waveController.stop();
    _waveController.reset();

    // Arrêter l'écoute des commandes vocales
    await _voiceCommandService.stopListening();
  }

  void _handleVoiceCommand(String command) {
    print('Commande vocale reçue: $command');

    // Arrêter l'écoute après avoir reçu une commande
    _stopListening();

    // Traiter les 3 commandes principales
    switch (command) {
      case 'accueil':
        setState(() {
          _selectedIndex = 0;
        });
        _ttsService.speak('Page d\'accueil');
        break;

      case 'appels':
        setState(() {
          _selectedIndex = 1;
        });
        _ttsService.speak('Page des appels');
        break;

      case 'messages':
        setState(() {
          _selectedIndex = 2;
        });
        _ttsService.speak('Page des messages');
        break;

      case 'time':
        // Le message vocal est géré par le service TTS
        break;

      default:
        if (command.startsWith('unknown:')) {
          // Afficher l'aide après le message vocal
          Future.delayed(const Duration(milliseconds: 2000), () {
            _showAvailableCommands();
          });
        }
    }
  }

  void _showAvailableCommands() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.mic, color: Color(0xFFFF7900)),
            SizedBox(width: 8),
            Text(
              'Commandes Vocales',
              style: TextStyle(
                color: Color(0xFFFF7900),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Interface simplifiée - 3 commandes principales :',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              _buildCommandItem('🏠', 'Accueil', 'Dites "accueil" ou "home"'),
              _buildCommandItem('📞', 'Appels', 'Dites "appels" ou "téléphone"'),
              _buildCommandItem('💬', 'Messages', 'Dites "messages" ou "sms"'),
              const SizedBox(height: 12),
              const Text(
                'Commande utile :',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              _buildCommandItem('🕐', 'Heure', 'Dites "heure" ou "quelle heure"'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Compris'),
          ),
        ],
      ),
    );
  }

  Widget _buildCommandItem(String emoji, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout() async {
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Déconnexion',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(0xFFFF7900),
            ),
          ),
          content: const Text(
            'Êtes-vous sûr de vouloir vous déconnecter ?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Déconnexion'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      try {
        await _authService.logout();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.logout, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text('Déconnexion réussie'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );

          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const SimpleUserTypeSelectionPage(),
            ),
            (route) => false,
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(child: Text('Erreur lors de la déconnexion: $e')),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  Widget _buildAnimatedMicButton() {
    return SizedBox(
      width: 80,
      height: 80,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Effet wave (ondulations)
          if (isListening) ...[
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Container(
                  width: 80 + (_waveAnimation.value * 40),
                  height: 80 + (_waveAnimation.value * 40),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFF7900).withValues(alpha: 0.3 - (_waveAnimation.value * 0.3)),
                      width: 2,
                    ),
                  ),
                );
              },
            ),
          ],

          // Bouton principal avec animation de scale
          ScaleTransition(
            scale: isListening ? _animation : const AlwaysStoppedAnimation(1.0),
            child: GestureDetector(
              onLongPress: _showAvailableCommands,
              child: Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isListening
                        ? [Colors.red, Colors.red.shade700]
                        : [const Color(0xFFFF7900), const Color(0xFFE66A00)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (isListening ? Colors.red : const Color(0xFFFF7900)).withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(35),
                    onTap: _toggleListening,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        isListening ? Icons.mic : Icons.mic_none,
                        size: 35,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          _pageTitles[_selectedIndex],
          style: const TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: Colors.black, size: 28),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(color: Color(0xFFFF7900)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    Icons.accessibility_new,
                    color: Colors.white,
                    size: 40,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Interface Simplifiée',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Canne Connectée',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.help, color: Color(0xFFFF7900)),
              title: const Text('Aide vocale'),
              onTap: () {
                Navigator.pop(context);
                _showAvailableCommands();
              },
            ),
            ListTile(
              leading: const Icon(Icons.volume_up, color: Color(0xFFFF7900)),
              title: const Text('Test vocal'),
              onTap: () {
                Navigator.pop(context);
                _ttsService.speak('Test de la synthèse vocale. Interface simplifiée avec trois sections principales.');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text(
                'Déconnexion',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
              ),
              onTap: () {
                Navigator.pop(context);
                _handleLogout();
              },
            ),
          ],
        ),
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: const Color(0xFFEEEEEE),
        selectedItemColor: const Color(0xFFFF7900),
        unselectedItemColor: Colors.black54,
        currentIndex: _selectedIndex,
        selectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
        ),
        onTap: (int index) {
          setState(() {
            _selectedIndex = index;
          });
          // Feedback vocal pour la navigation
          _ttsService.speak(_pageTitles[index]);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home, size: 32),
            label: 'Accueil',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.phone, size: 32),
            label: 'Appels',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.message, size: 32),
            label: 'Messages',
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildAnimatedMicButton(),
          // Indicateur d'aide ou de statut TTS
          if (!isListening)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: isSpeaking
                    ? Colors.green.withOpacity(0.8)
                    : Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isSpeaking) ...[
                    const Icon(
                      Icons.volume_up,
                      color: Colors.white,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                  ],
                  Flexible(
                    child: Text(
                      isSpeaking ? 'Réponse vocale...' : 'Appui long = Aide',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _waveController.dispose();
    _voiceCommandService.dispose();
    _ttsService.dispose();
    super.dispose();
  }
}

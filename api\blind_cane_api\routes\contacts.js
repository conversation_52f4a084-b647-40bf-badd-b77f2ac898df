const express = require('express');
const { body, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/contacts
 * @desc Récupère la liste des contacts
 */
router.get('/', authMiddleware, [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('type').optional().isIn(['famille', 'ami', 'professionnel', 'urgence']).withMessage('Type invalide'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const { user_id, type, limit = 50 } = req.query;

    // Simulation de données de contacts
    const contacts = [
      {
        id: 1,
        nom: 'Dupont',
        prenom: '<PERSON>',
        telephone: '+33123456789',
        email: '<EMAIL>',
        type: 'famille',
        is_urgence: true,
        user_id: user_id || 1,
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        nom: 'Martin',
        prenom: 'Pierre',
        telephone: '+33987654321',
        email: '<EMAIL>',
        type: 'ami',
        is_urgence: false,
        user_id: user_id || 1,
        created_at: new Date().toISOString()
      }
    ];

    let filteredContacts = contacts;
    
    if (type) {
      filteredContacts = filteredContacts.filter(contact => contact.type === type);
    }

    if (user_id) {
      filteredContacts = filteredContacts.filter(contact => contact.user_id === parseInt(user_id));
    }

    res.json({
      contacts: filteredContacts.slice(0, parseInt(limit)),
      total: filteredContacts.length
    });

  } catch (error) {
    console.error('Erreur récupération contacts:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des contacts',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/contacts
 * @desc Crée un nouveau contact
 */
router.post('/', authMiddleware, [
  body('nom')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('prenom')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le prénom doit contenir entre 2 et 100 caractères'),
  
  body('telephone')
    .isMobilePhone('fr-FR')
    .withMessage('Numéro de téléphone invalide'),
  
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  
  body('type')
    .isIn(['famille', 'ami', 'professionnel', 'urgence'])
    .withMessage('Type de contact invalide'),
  
  body('is_urgence')
    .optional()
    .isBoolean()
    .withMessage('is_urgence doit être un booléen')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { nom, prenom, telephone, email, type, is_urgence = false } = req.body;

    // Simulation de création de contact
    const newContact = {
      id: Date.now(), // ID temporaire
      nom,
      prenom,
      telephone,
      email,
      type,
      is_urgence,
      user_id: req.user?.userId || 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.status(201).json({
      message: 'Contact créé avec succès',
      contact: newContact
    });

  } catch (error) {
    console.error('Erreur création contact:', error);
    res.status(500).json({
      error: 'Erreur lors de la création du contact',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/contacts/:id
 * @desc Récupère un contact spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const contactId = parseInt(req.params.id);
    
    if (isNaN(contactId)) {
      return res.status(400).json({
        error: 'ID contact invalide'
      });
    }

    // Simulation de récupération de contact
    const contact = {
      id: contactId,
      nom: 'Dupont',
      prenom: 'Marie',
      telephone: '+33123456789',
      email: '<EMAIL>',
      type: 'famille',
      is_urgence: true,
      user_id: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.json({
      contact
    });

  } catch (error) {
    console.error('Erreur récupération contact:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération du contact',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/contacts/:id
 * @desc Met à jour un contact
 */
router.put('/:id', authMiddleware, [
  body('nom')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('prenom')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le prénom doit contenir entre 2 et 100 caractères'),
  
  body('telephone')
    .optional()
    .isMobilePhone('fr-FR')
    .withMessage('Numéro de téléphone invalide'),
  
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  
  body('type')
    .optional()
    .isIn(['famille', 'ami', 'professionnel', 'urgence'])
    .withMessage('Type de contact invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const contactId = parseInt(req.params.id);
    
    if (isNaN(contactId)) {
      return res.status(400).json({
        error: 'ID contact invalide'
      });
    }

    // Simulation de mise à jour
    const updatedContact = {
      id: contactId,
      ...req.body,
      updated_at: new Date().toISOString()
    };

    res.json({
      message: 'Contact mis à jour avec succès',
      contact: updatedContact
    });

  } catch (error) {
    console.error('Erreur mise à jour contact:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour du contact',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/contacts/:id
 * @desc Supprime un contact
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const contactId = parseInt(req.params.id);
    
    if (isNaN(contactId)) {
      return res.status(400).json({
        error: 'ID contact invalide'
      });
    }

    res.json({
      message: 'Contact supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression contact:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du contact',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

const express = require('express');
const { body, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/appels
 * @desc Récupère l'historique des appels
 */
router.get('/', authMiddleware, [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('type').optional().isIn(['entrant', 'sortant', 'manque']).withMessage('Type invalide'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const { user_id, type, limit = 50, offset = 0 } = req.query;

    // Simulation de données d'appels
    const appels = [
      {
        id: 1,
        appelant_id: 1,
        destinataire_id: 2,
        numero_telephone: '+33123456789',
        type: 'sortant',
        duree: 120, // en secondes
        date_appel: new Date(Date.now() - 3600000).toISOString(), // il y a 1h
        statut: 'termine',
        contact_nom: 'Marie Dupont'
      },
      {
        id: 2,
        appelant_id: 2,
        destinataire_id: 1,
        numero_telephone: '+33987654321',
        type: 'entrant',
        duree: 0,
        date_appel: new Date(Date.now() - 7200000).toISOString(), // il y a 2h
        statut: 'manque',
        contact_nom: 'Pierre Martin'
      },
      {
        id: 3,
        appelant_id: 1,
        destinataire_id: 3,
        numero_telephone: '+33555666777',
        type: 'sortant',
        duree: 300,
        date_appel: new Date(Date.now() - 10800000).toISOString(), // il y a 3h
        statut: 'termine',
        contact_nom: 'Dr. Leblanc'
      }
    ];

    let filteredAppels = appels;
    
    if (user_id) {
      filteredAppels = filteredAppels.filter(appel => 
        appel.appelant_id === parseInt(user_id) || appel.destinataire_id === parseInt(user_id)
      );
    }

    if (type) {
      filteredAppels = filteredAppels.filter(appel => appel.type === type);
    }

    const paginatedAppels = filteredAppels.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

    res.json({
      appels: paginatedAppels,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: filteredAppels.length
      }
    });

  } catch (error) {
    console.error('Erreur récupération appels:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des appels',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/appels
 * @desc Enregistre un nouvel appel
 */
router.post('/', authMiddleware, [
  body('destinataire_id')
    .optional()
    .isInt()
    .withMessage('ID destinataire invalide'),
  
  body('numero_telephone')
    .isMobilePhone('fr-FR')
    .withMessage('Numéro de téléphone invalide'),
  
  body('type')
    .isIn(['entrant', 'sortant', 'manque'])
    .withMessage('Type d\'appel invalide'),
  
  body('duree')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Durée invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { destinataire_id, numero_telephone, type, duree = 0 } = req.body;

    // Simulation d'enregistrement d'appel
    const newAppel = {
      id: Date.now(),
      appelant_id: req.user?.userId || 1,
      destinataire_id: destinataire_id || null,
      numero_telephone,
      type,
      duree: parseInt(duree),
      date_appel: new Date().toISOString(),
      statut: duree > 0 ? 'termine' : 'manque'
    };

    res.status(201).json({
      message: 'Appel enregistré avec succès',
      appel: newAppel
    });

  } catch (error) {
    console.error('Erreur enregistrement appel:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'enregistrement de l\'appel',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/appels/:id
 * @desc Récupère un appel spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const appelId = parseInt(req.params.id);
    
    if (isNaN(appelId)) {
      return res.status(400).json({
        error: 'ID appel invalide'
      });
    }

    // Simulation de récupération d'appel
    const appel = {
      id: appelId,
      appelant_id: 1,
      destinataire_id: 2,
      numero_telephone: '+33123456789',
      type: 'sortant',
      duree: 120,
      date_appel: new Date().toISOString(),
      statut: 'termine',
      contact_nom: 'Marie Dupont'
    };

    res.json({
      appel
    });

  } catch (error) {
    console.error('Erreur récupération appel:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération de l\'appel',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/appels/:id
 * @desc Met à jour un appel
 */
router.put('/:id', authMiddleware, [
  body('duree')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Durée invalide'),
  
  body('statut')
    .optional()
    .isIn(['en_cours', 'termine', 'manque', 'rejete'])
    .withMessage('Statut invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const appelId = parseInt(req.params.id);
    
    if (isNaN(appelId)) {
      return res.status(400).json({
        error: 'ID appel invalide'
      });
    }

    // Simulation de mise à jour
    const updatedAppel = {
      id: appelId,
      ...req.body,
      updated_at: new Date().toISOString()
    };

    res.json({
      message: 'Appel mis à jour avec succès',
      appel: updatedAppel
    });

  } catch (error) {
    console.error('Erreur mise à jour appel:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour de l\'appel',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/appels/:id
 * @desc Supprime un appel de l'historique
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const appelId = parseInt(req.params.id);
    
    if (isNaN(appelId)) {
      return res.status(400).json({
        error: 'ID appel invalide'
      });
    }

    res.json({
      message: 'Appel supprimé de l\'historique avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression appel:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression de l\'appel',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/appels/stats/summary
 * @desc Récupère les statistiques des appels
 */
router.get('/stats/summary', authMiddleware, async (req, res) => {
  try {
    // Simulation de statistiques
    const stats = {
      total_appels: 25,
      appels_entrants: 12,
      appels_sortants: 10,
      appels_manques: 3,
      duree_totale: 3600, // en secondes
      duree_moyenne: 144, // en secondes
      dernier_appel: new Date().toISOString()
    };

    res.json({
      stats
    });

  } catch (error) {
    console.error('Erreur récupération statistiques:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

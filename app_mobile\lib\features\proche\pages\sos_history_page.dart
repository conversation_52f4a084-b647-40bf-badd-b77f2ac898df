import 'package:flutter/material.dart';

/// Page d'historique des alertes SOS
class SOSHistoryPage extends StatefulWidget {
  const SOSHistoryPage({super.key});

  @override
  State<SOSHistoryPage> createState() => _SOSHistoryPageState();
}

class _SOSHistoryPageState extends State<SOSHistoryPage> {
  String _selectedFilter = 'Toutes';
  final List<String> _filters = ['Toutes', 'Critiques', 'Urgentes', 'Modérées', 'Résolues'];

  // Données simulées d'alertes SOS
  final List<SOSAlert> _alerts = [
    SOSAlert(
      id: '1',
      type: 'Médicale',
      message: 'Malaise général, besoin d\'aide immédiate',
      urgencyLevel: 'Critique',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      status: 'Résolue',
      location: 'Plateau, Abidjan',
      coordinates: '5.3196°N, 4.0255°O',
      responseTime: const Duration(minutes: 8),
    ),
    SOSAlert(
      id: '2',
      type: 'Chute',
      message: 'Chute détectée par la canne connectée',
      urgencyLevel: 'Urgent',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      status: 'Résolue',
      location: 'Cocody, Abidjan',
      coordinates: '5.3444°N, 3.9738°O',
      responseTime: const Duration(minutes: 12),
    ),
    SOSAlert(
      id: '3',
      type: 'Assistance',
      message: 'Besoin d\'aide pour traverser la route',
      urgencyLevel: 'Modérée',
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      status: 'Résolue',
      location: 'Treichville, Abidjan',
      coordinates: '5.2767°N, 4.0081°O',
      responseTime: const Duration(minutes: 5),
    ),
    SOSAlert(
      id: '4',
      type: 'Urgence',
      message: 'Situation d\'urgence, intervention requise',
      urgencyLevel: 'Critique',
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
      status: 'En cours',
      location: 'Adjamé, Abidjan',
      coordinates: '5.3667°N, 4.0167°O',
      responseTime: null,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final filteredAlerts = _getFilteredAlerts();

    return Column(
      children: [
        // Panneau de filtres et statistiques
        _buildHeaderPanel(),
        
        // Liste des alertes
        Expanded(
          child: filteredAlerts.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredAlerts.length,
                  itemBuilder: (context, index) {
                    return _buildAlertCard(filteredAlerts[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildHeaderPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Statistiques rapides
          Row(
            children: [
              _buildStatCard('Total', _alerts.length.toString(), Colors.blue),
              const SizedBox(width: 12),
              _buildStatCard('Critiques', _alerts.where((a) => a.urgencyLevel == 'Critique').length.toString(), Colors.red),
              const SizedBox(width: 12),
              _buildStatCard('Résolues', _alerts.where((a) => a.status == 'Résolue').length.toString(), Colors.green),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Filtres
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _filters.map((filter) {
                final isSelected = _selectedFilter == filter;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(filter),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    },
                    selectedColor: const Color(0xFFFF7900).withOpacity(0.2),
                    checkmarkColor: const Color(0xFFFF7900),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertCard(SOSAlert alert) {
    final urgencyColor = _getUrgencyColor(alert.urgencyLevel);
    final statusColor = _getStatusColor(alert.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showAlertDetails(alert),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec type et statut
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: urgencyColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      alert.type,
                      style: TextStyle(
                        color: urgencyColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      alert.status,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Message
              Text(
                alert.message,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // Informations détaillées
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(alert.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      alert.location,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              
              if (alert.responseTime != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.timer, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Temps de réponse: ${alert.responseTime!.inMinutes} min',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune alerte SOS',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Les alertes SOS apparaîtront ici',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<SOSAlert> _getFilteredAlerts() {
    if (_selectedFilter == 'Toutes') {
      return _alerts;
    }
    
    return _alerts.where((alert) {
      switch (_selectedFilter) {
        case 'Critiques':
          return alert.urgencyLevel == 'Critique';
        case 'Urgentes':
          return alert.urgencyLevel == 'Urgent';
        case 'Modérées':
          return alert.urgencyLevel == 'Modérée';
        case 'Résolues':
          return alert.status == 'Résolue';
        default:
          return true;
      }
    }).toList();
  }

  Color _getUrgencyColor(String urgency) {
    switch (urgency) {
      case 'Critique':
        return Colors.red;
      case 'Urgent':
        return Colors.orange;
      case 'Modérée':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Résolue':
        return Colors.green;
      case 'En cours':
        return Colors.orange;
      case 'En attente':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return 'Il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return 'Il y a ${difference.inMinutes}min';
    } else {
      return 'À l\'instant';
    }
  }

  void _showAlertDetails(SOSAlert alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.warning,
              color: _getUrgencyColor(alert.urgencyLevel),
            ),
            const SizedBox(width: 8),
            Text('Alerte ${alert.type}'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Message', alert.message),
              _buildDetailRow('Niveau d\'urgence', alert.urgencyLevel),
              _buildDetailRow('Statut', alert.status),
              _buildDetailRow('Date et heure', _formatDateTime(alert.timestamp)),
              _buildDetailRow('Lieu', alert.location),
              _buildDetailRow('Coordonnées', alert.coordinates),
              if (alert.responseTime != null)
                _buildDetailRow('Temps de réponse', '${alert.responseTime!.inMinutes} minutes'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          if (alert.status != 'Résolue')
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Marquer comme résolue
                setState(() {
                  final index = _alerts.indexWhere((a) => a.id == alert.id);
                  if (index != -1) {
                    _alerts[index] = alert.copyWith(status: 'Résolue');
                  }
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: const Text('Marquer résolue', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

/// Modèle d'alerte SOS
class SOSAlert {
  final String id;
  final String type;
  final String message;
  final String urgencyLevel;
  final DateTime timestamp;
  final String status;
  final String location;
  final String coordinates;
  final Duration? responseTime;

  SOSAlert({
    required this.id,
    required this.type,
    required this.message,
    required this.urgencyLevel,
    required this.timestamp,
    required this.status,
    required this.location,
    required this.coordinates,
    this.responseTime,
  });

  SOSAlert copyWith({
    String? id,
    String? type,
    String? message,
    String? urgencyLevel,
    DateTime? timestamp,
    String? status,
    String? location,
    String? coordinates,
    Duration? responseTime,
  }) {
    return SOSAlert(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      urgencyLevel: urgencyLevel ?? this.urgencyLevel,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      responseTime: responseTime ?? this.responseTime,
    );
  }
}

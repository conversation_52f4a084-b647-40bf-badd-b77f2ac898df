const express = require('express');
const { body, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/messages
 * @desc Récupère la liste des messages
 */
router.get('/', authMiddleware, [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('contact_id').optional().isInt().withMessage('ID contact invalide'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const { user_id, contact_id, limit = 50, offset = 0 } = req.query;

    // Simulation de données de messages
    const messages = [
      {
        id: 1,
        contenu: 'Bonjour, comment allez-vous ?',
        expediteur_id: 1,
        destinataire_id: 2,
        date_envoi: new Date().toISOString(),
        lu: false,
        type: 'text'
      },
      {
        id: 2,
        contenu: 'Tout va bien, merci !',
        expediteur_id: 2,
        destinataire_id: 1,
        date_envoi: new Date().toISOString(),
        lu: true,
        type: 'text'
      }
    ];

    let filteredMessages = messages;
    
    if (user_id) {
      filteredMessages = filteredMessages.filter(msg => 
        msg.expediteur_id === parseInt(user_id) || msg.destinataire_id === parseInt(user_id)
      );
    }

    if (contact_id) {
      filteredMessages = filteredMessages.filter(msg => 
        msg.expediteur_id === parseInt(contact_id) || msg.destinataire_id === parseInt(contact_id)
      );
    }

    const paginatedMessages = filteredMessages.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

    res.json({
      messages: paginatedMessages,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: filteredMessages.length
      }
    });

  } catch (error) {
    console.error('Erreur récupération messages:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des messages',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/messages
 * @desc Envoie un nouveau message
 */
router.post('/', authMiddleware, [
  body('contenu')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Le contenu doit contenir entre 1 et 1000 caractères'),
  
  body('destinataire_id')
    .isInt()
    .withMessage('ID destinataire invalide'),
  
  body('type')
    .optional()
    .isIn(['text', 'audio', 'image', 'urgence'])
    .withMessage('Type de message invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { contenu, destinataire_id, type = 'text' } = req.body;

    // Simulation d'envoi de message
    const newMessage = {
      id: Date.now(),
      contenu,
      expediteur_id: req.user?.userId || 1,
      destinataire_id: parseInt(destinataire_id),
      date_envoi: new Date().toISOString(),
      lu: false,
      type
    };

    res.status(201).json({
      message: 'Message envoyé avec succès',
      data: newMessage
    });

  } catch (error) {
    console.error('Erreur envoi message:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'envoi du message',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/messages/:id
 * @desc Récupère un message spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    
    if (isNaN(messageId)) {
      return res.status(400).json({
        error: 'ID message invalide'
      });
    }

    // Simulation de récupération de message
    const message = {
      id: messageId,
      contenu: 'Contenu du message',
      expediteur_id: 1,
      destinataire_id: 2,
      date_envoi: new Date().toISOString(),
      lu: false,
      type: 'text'
    };

    res.json({
      message
    });

  } catch (error) {
    console.error('Erreur récupération message:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération du message',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/messages/:id/read
 * @desc Marque un message comme lu
 */
router.put('/:id/read', authMiddleware, async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    
    if (isNaN(messageId)) {
      return res.status(400).json({
        error: 'ID message invalide'
      });
    }

    // Simulation de marquage comme lu
    const updatedMessage = {
      id: messageId,
      lu: true,
      date_lecture: new Date().toISOString()
    };

    res.json({
      message: 'Message marqué comme lu',
      data: updatedMessage
    });

  } catch (error) {
    console.error('Erreur marquage message lu:', error);
    res.status(500).json({
      error: 'Erreur lors du marquage du message',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/messages/:id
 * @desc Supprime un message
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    
    if (isNaN(messageId)) {
      return res.status(400).json({
        error: 'ID message invalide'
      });
    }

    res.json({
      message: 'Message supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression message:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du message',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/messages/bulk-read
 * @desc Marque plusieurs messages comme lus
 */
router.post('/bulk-read', authMiddleware, [
  body('message_ids')
    .isArray()
    .withMessage('message_ids doit être un tableau'),
  
  body('message_ids.*')
    .isInt()
    .withMessage('Chaque ID de message doit être un entier')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { message_ids } = req.body;

    // Simulation de marquage en masse
    const updatedCount = message_ids.length;

    res.json({
      message: `${updatedCount} messages marqués comme lus`,
      updated_count: updatedCount
    });

  } catch (error) {
    console.error('Erreur marquage en masse:', error);
    res.status(500).json({
      error: 'Erreur lors du marquage en masse',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

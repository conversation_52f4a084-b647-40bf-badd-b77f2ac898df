1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.canne_connectee"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:5-66
15-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:2:5-78
16-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:2:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:3:5-80
17-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:3:22-78
18    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
18-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:4:5-84
18-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:4:22-82
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:5:5-64
19-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:5:22-62
20    <uses-permission android:name="android.permission.RECORD_AUDIO" />
20-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:6:5-70
20-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:6:22-68
21    <uses-permission
21-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:7:5-80
22        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
22-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:7:22-78
23        android:maxSdkVersion="28" />
23-->[:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Permissions pour accéder aux appels -->
24-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:8:5-79
24-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:8:22-77
25    <uses-permission android:name="android.permission.READ_CALL_LOG" />
25-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:12:5-71
25-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:12:22-69
26    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
26-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:13:5-72
26-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:13:22-70
27    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- Permissions pour accéder aux SMS -->
27-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:14:5-68
27-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:14:22-66
28    <uses-permission android:name="android.permission.READ_SMS" />
28-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:17:5-66
28-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:17:22-64
29    <uses-permission android:name="android.permission.SEND_SMS" />
29-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:18:5-66
29-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:18:22-64
30    <uses-permission android:name="android.permission.RECEIVE_SMS" /> <!-- Permissions pour accéder aux contacts -->
30-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:19:5-69
30-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:19:22-67
31    <uses-permission android:name="android.permission.READ_CONTACTS" />
31-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:22:5-71
31-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:22:22-69
32    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
32-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:23:5-72
32-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:23:22-70
33    <!--
34 Required to query activities that can process text, see:
35         https://developer.android.com/training/package-visibility and
36         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
37
38         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
39    -->
40    <queries>
40-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:61:5-66:15
41        <intent>
41-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:62:9-65:18
42            <action android:name="android.intent.action.PROCESS_TEXT" />
42-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:63:13-72
42-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:63:21-70
43
44            <data android:mimeType="text/plain" />
44-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:13-50
44-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:19-48
45        </intent>
46    </queries>
47
48    <uses-feature android:name="android.hardware.camera.any" />
48-->[:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
48-->[:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
49
50    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
50-->[:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
50-->[:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.example.canne_connectee.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.example.canne_connectee.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
60        android:name="android.app.Application"
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:icon="@mipmap/ic_launcher"
65        android:label="canne_connectee" >
66        <activity
67            android:name="com.example.canne_connectee.MainActivity"
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
69            android:exported="true"
70            android:hardwareAccelerated="true"
71            android:launchMode="singleTop"
72            android:taskAffinity=""
73            android:theme="@style/LaunchTheme"
74            android:windowSoftInputMode="adjustResize" >
75
76            <!--
77                 Specifies an Android theme to apply to this Activity as soon as
78                 the Android process has started. This theme is visible to the user
79                 while the Flutter UI initializes. After that, this theme continues
80                 to determine the Window background behind the Flutter UI.
81            -->
82            <meta-data
83                android:name="io.flutter.embedding.android.NormalTheme"
84                android:resource="@style/NormalTheme" />
85
86            <intent-filter>
87                <action android:name="android.intent.action.MAIN" />
88
89                <category android:name="android.intent.category.LAUNCHER" />
90            </intent-filter>
91        </activity>
92        <!--
93             Don't delete the meta-data below.
94             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
95        -->
96        <meta-data
97            android:name="flutterEmbedding"
98            android:value="2" />
99
100        <service
100-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
101            android:name="androidx.camera.core.impl.MetadataHolderService"
101-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
102            android:enabled="false"
102-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
103            android:exported="false" >
103-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
105                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
105-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
106                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
106-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
107        </service>
108        <service
108-->[:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
109            android:name="com.baseflow.geolocator.GeolocatorLocationService"
109-->[:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
110            android:enabled="true"
110-->[:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
111            android:exported="false"
111-->[:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
112            android:foregroundServiceType="location" />
112-->[:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
113
114        <activity
114-->[:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
115            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
115-->[:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
116            android:exported="false"
116-->[:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
117            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
117-->[:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
118
119        <service
119-->[:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
120            android:name="com.google.firebase.components.ComponentDiscoveryService"
120-->[:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
121            android:directBootAware="true"
121-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
122            android:exported="false" >
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
123            <meta-data
123-->[:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
124                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
124-->[:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
126            <meta-data
126-->[:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
127                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
127-->[:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
129            <meta-data
129-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
130                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
130-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
132            <meta-data
132-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
133                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
133-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
135            <meta-data
135-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
136                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
136-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
138        </service>
139
140        <activity
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
141            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
141-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
142            android:excludeFromRecents="true"
142-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
143            android:exported="true"
143-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
144            android:launchMode="singleTask"
144-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
145            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
145-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
146            <intent-filter>
146-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
147                <action android:name="android.intent.action.VIEW" />
147-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
147-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
148
149                <category android:name="android.intent.category.DEFAULT" />
149-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
149-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
150                <category android:name="android.intent.category.BROWSABLE" />
150-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
150-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
151
152                <data
152-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:13-50
153                    android:host="firebase.auth"
154                    android:path="/"
155                    android:scheme="genericidp" />
156            </intent-filter>
157        </activity>
158        <activity
158-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
159            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
159-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
160            android:excludeFromRecents="true"
160-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
161            android:exported="true"
161-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
162            android:launchMode="singleTask"
162-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
163            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
163-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
164            <intent-filter>
164-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
165                <action android:name="android.intent.action.VIEW" />
165-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
165-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
166
167                <category android:name="android.intent.category.DEFAULT" />
167-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
167-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
168                <category android:name="android.intent.category.BROWSABLE" />
168-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
168-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
169
170                <data
170-->C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:13-50
171                    android:host="firebase.auth"
172                    android:path="/"
173                    android:scheme="recaptcha" />
174            </intent-filter>
175        </activity>
176
177        <uses-library
177-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
178            android:name="androidx.window.extensions"
178-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
179            android:required="false" />
179-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
180        <uses-library
180-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
181            android:name="androidx.window.sidecar"
181-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
182            android:required="false" />
182-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
183
184        <service
184-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
185            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
185-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
186            android:enabled="true"
186-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
187            android:exported="false" >
187-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
188            <meta-data
188-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
189                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
189-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
190                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
190-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
191        </service>
192
193        <activity
193-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
194            android:name="androidx.credentials.playservices.HiddenActivity"
194-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
195            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
195-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
196            android:enabled="true"
196-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
197            android:exported="false"
197-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
198            android:fitsSystemWindows="true"
198-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
199            android:theme="@style/Theme.Hidden" >
199-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
200        </activity>
201        <activity
201-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
202            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
202-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
203            android:excludeFromRecents="true"
203-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
204            android:exported="false"
204-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
205            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
205-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
206        <!--
207            Service handling Google Sign-In user revocation. For apps that do not integrate with
208            Google Sign-In, this service will never be started.
209        -->
210        <service
210-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
211            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
211-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
212            android:exported="true"
212-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
213            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
213-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
214            android:visibleToInstantApps="true" />
214-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
215
216        <activity
216-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
217            android:name="com.google.android.gms.common.api.GoogleApiActivity"
217-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
218            android:exported="false"
218-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
219            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
219-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
220
221        <provider
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
222            android:name="com.google.firebase.provider.FirebaseInitProvider"
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
223            android:authorities="com.example.canne_connectee.firebaseinitprovider"
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
224            android:directBootAware="true"
224-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
225            android:exported="false"
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
226            android:initOrder="100" />
226-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
227        <provider
227-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
228            android:name="androidx.startup.InitializationProvider"
228-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
229            android:authorities="com.example.canne_connectee.androidx-startup"
229-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
230            android:exported="false" >
230-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
231            <meta-data
231-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
232                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
232-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
233                android:value="androidx.startup" />
233-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
234            <meta-data
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
235                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
236                android:value="androidx.startup" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
237        </provider>
238
239        <meta-data
239-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
240            android:name="com.google.android.gms.version"
240-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
241            android:value="@integer/google_play_services_version" />
241-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
242
243        <receiver
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
244            android:name="androidx.profileinstaller.ProfileInstallReceiver"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
245            android:directBootAware="false"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
246            android:enabled="true"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
247            android:exported="true"
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
248            android:permission="android.permission.DUMP" >
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
250                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
253                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
254            </intent-filter>
255            <intent-filter>
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
256                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
257            </intent-filter>
258            <intent-filter>
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
259                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
260            </intent-filter>
261        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
262        <activity
262-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
263            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
263-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
264            android:exported="false"
264-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
265            android:stateNotNeeded="true"
265-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
266            android:theme="@style/Theme.PlayCore.Transparent" />
266-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
267    </application>
268
269</manifest>

const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');

/**
 * Modèle User pour la gestion des utilisateurs
 */
class User {
  constructor(data) {
    this.id = data.id;
    this.nom_utilisateur = data.nom_utilisateur;
    this.prenom_utilisateur = data.prenom_utilisateur;
    this.email_utilisateur = data.email_utilisateur;
    this.mot_de_passe = data.mot_de_passe;
    this.contact_utilisateur = data.contact_utilisateur;
    this.adresse_utilisateur = data.adresse_utilisateur;
    this.isActive = data.is_active !== undefined ? data.is_active : true;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  /**
   * Crée un nouvel utilisateur
   */
  static async create(userData) {
    try {
      // Hacher le mot de passe
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(userData.mot_de_passe, saltRounds);

      // Simulation de création d'utilisateur (remplacer par vraie DB)
      const newUser = new User({
        id: Date.now(), // ID temporaire
        nom_utilisateur: userData.nom_utilisateur,
        prenom_utilisateur: userData.prenom_utilisateur,
        email_utilisateur: userData.email_utilisateur,
        mot_de_passe: hashedPassword,
        contact_utilisateur: userData.contact_utilisateur,
        adresse_utilisateur: userData.adresse_utilisateur,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      });

      return newUser;
    } catch (error) {
      throw new Error(`Erreur lors de la création de l'utilisateur: ${error.message}`);
    }
  }

  /**
   * Trouve un utilisateur par email
   */
  static async findByEmail(email) {
    try {
      // Simulation de recherche par email (remplacer par vraie DB)
      // Pour les tests, on retourne null (utilisateur non trouvé)
      return null;
    } catch (error) {
      throw new Error(`Erreur lors de la recherche par email: ${error.message}`);
    }
  }

  /**
   * Trouve un utilisateur par ID
   */
  static async findById(id) {
    try {
      // Simulation de recherche par ID (remplacer par vraie DB)
      if (id === 1) {
        return new User({
          id: 1,
          nom_utilisateur: 'Dupont',
          prenom_utilisateur: 'Jean',
          email_utilisateur: '<EMAIL>',
          mot_de_passe: '$2a$12$hashedpassword',
          contact_utilisateur: '+33123456789',
          adresse_utilisateur: '123 Rue de la Paix, Paris',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
      return null;
    } catch (error) {
      throw new Error(`Erreur lors de la recherche par ID: ${error.message}`);
    }
  }

  /**
   * Trouve tous les utilisateurs avec pagination
   */
  static async findAll(options = {}) {
    try {
      const { limit = 20, offset = 0 } = options;
      
      // Simulation de données utilisateurs
      const users = [
        new User({
          id: 1,
          nom_utilisateur: 'Dupont',
          prenom_utilisateur: 'Jean',
          email_utilisateur: '<EMAIL>',
          contact_utilisateur: '+33123456789',
          adresse_utilisateur: '123 Rue de la Paix, Paris',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }),
        new User({
          id: 2,
          nom_utilisateur: 'Martin',
          prenom_utilisateur: 'Marie',
          email_utilisateur: '<EMAIL>',
          contact_utilisateur: '+33987654321',
          adresse_utilisateur: '456 Avenue des Champs, Lyon',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        })
      ];

      return users.slice(offset, offset + limit);
    } catch (error) {
      throw new Error(`Erreur lors de la récupération des utilisateurs: ${error.message}`);
    }
  }

  /**
   * Vérifie le mot de passe
   */
  async verifyPassword(password) {
    try {
      return await bcrypt.compare(password, this.mot_de_passe);
    } catch (error) {
      throw new Error(`Erreur lors de la vérification du mot de passe: ${error.message}`);
    }
  }

  /**
   * Met à jour l'utilisateur
   */
  async update(updateData) {
    try {
      // Mettre à jour les champs fournis
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && this.hasOwnProperty(key)) {
          this[key] = updateData[key];
        }
      });

      this.updated_at = new Date();

      // Simulation de mise à jour en base (remplacer par vraie DB)
      return this;
    } catch (error) {
      throw new Error(`Erreur lors de la mise à jour: ${error.message}`);
    }
  }

  /**
   * Supprime l'utilisateur
   */
  async delete() {
    try {
      // Simulation de suppression (remplacer par vraie DB)
      this.isActive = false;
      this.updated_at = new Date();
      return true;
    } catch (error) {
      throw new Error(`Erreur lors de la suppression: ${error.message}`);
    }
  }

  /**
   * Convertit l'utilisateur en objet JSON (sans le mot de passe)
   */
  toJSON() {
    const userObj = { ...this };
    delete userObj.mot_de_passe; // Ne jamais exposer le mot de passe
    return userObj;
  }

  /**
   * Valide les données utilisateur
   */
  static validateUserData(userData) {
    const errors = [];

    if (!userData.nom_utilisateur || userData.nom_utilisateur.length < 2) {
      errors.push('Le nom doit contenir au moins 2 caractères');
    }

    if (!userData.prenom_utilisateur || userData.prenom_utilisateur.length < 2) {
      errors.push('Le prénom doit contenir au moins 2 caractères');
    }

    if (!userData.email_utilisateur || !this.isValidEmail(userData.email_utilisateur)) {
      errors.push('Email invalide');
    }

    if (!userData.mot_de_passe || userData.mot_de_passe.length < 6) {
      errors.push('Le mot de passe doit contenir au moins 6 caractères');
    }

    return errors;
  }

  /**
   * Valide le format email
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = User;

  Activity android.app  Bundle android.app.Activity  Log android.app.Activity  SignInWithApplePlugin android.app.Activity  TAG android.app.Activity  equals android.app.Activity  finish android.app.Activity  getPACKAGEManager android.app.Activity  getPACKAGEName android.app.Activity  getPackageManager android.app.Activity  getPackageName android.app.Activity  onCreate android.app.Activity  packageManager android.app.Activity  packageName android.app.Activity  setPackageManager android.app.Activity  setPackageName android.app.Activity  
startActivity android.app.Activity  startActivityForResult android.app.Activity  Intent android.content  Bundle android.content.Context  Log android.content.Context  SignInWithApplePlugin android.content.Context  TAG android.content.Context  finish android.content.Context  onCreate android.content.Context  
startActivity android.content.Context  startActivityForResult android.content.Context  Bundle android.content.ContextWrapper  Log android.content.ContextWrapper  SignInWithApplePlugin android.content.ContextWrapper  TAG android.content.ContextWrapper  finish android.content.ContextWrapper  onCreate android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startActivityForResult android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  data android.content.Intent  flags android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  setData android.content.Intent  setFlags android.content.Intent  
setPackage android.content.Intent  getLaunchIntentForPackage !android.content.pm.PackageManager  Uri android.net  parse android.net.Uri  toString android.net.Uri  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  SignInWithApplePlugin  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startActivityForResult  android.view.ContextThemeWrapper  NonNull androidx.annotation  CustomTabsIntent androidx.browser.customtabs  Builder ,androidx.browser.customtabs.CustomTabsIntent  intent ,androidx.browser.customtabs.CustomTabsIntent  startAnimationBundle ,androidx.browser.customtabs.CustomTabsIntent  build 4androidx.browser.customtabs.CustomTabsIntent.Builder  Boolean -com.aboutyou.dart_packages.sign_in_with_apple  CustomTabsIntent -com.aboutyou.dart_packages.sign_in_with_apple  Int -com.aboutyou.dart_packages.sign_in_with_apple  Intent -com.aboutyou.dart_packages.sign_in_with_apple  Log -com.aboutyou.dart_packages.sign_in_with_apple  
MethodChannel -com.aboutyou.dart_packages.sign_in_with_apple  SignInWithAppleCallback -com.aboutyou.dart_packages.sign_in_with_apple  SignInWithApplePlugin -com.aboutyou.dart_packages.sign_in_with_apple  String -com.aboutyou.dart_packages.sign_in_with_apple  TAG -com.aboutyou.dart_packages.sign_in_with_apple  Unit -com.aboutyou.dart_packages.sign_in_with_apple  Uri -com.aboutyou.dart_packages.sign_in_with_apple  lastAuthorizationRequestResult -com.aboutyou.dart_packages.sign_in_with_apple  (triggerMainActivityToHideChromeCustomTab -com.aboutyou.dart_packages.sign_in_with_apple  Bundle Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  Log Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  SignInWithApplePlugin Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  TAG Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  finish Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  	getINTENT Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  	getIntent Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  intent Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  	setIntent Ecom.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback  ActivityPluginBinding Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Boolean Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  CUSTOM_TABS_REQUEST_CODE Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  CustomTabsIntent Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
FlutterPlugin Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Int Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Intent Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
MethodCall Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  
MethodChannel Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  NonNull Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Result Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  String Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Unit Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  Uri Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  binding Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  channel Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  !getLASTAuthorizationRequestResult Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  !getLastAuthorizationRequestResult Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  +getTRIGGERMainActivityToHideChromeCustomTab Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  +getTriggerMainActivityToHideChromeCustomTab Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  lastAuthorizationRequestResult Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  onAttachedToActivity Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  onDetachedFromActivity Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  (triggerMainActivityToHideChromeCustomTab Ccom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin  ActivityPluginBinding Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Boolean Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  CustomTabsIntent Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  
FlutterPlugin Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Int Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Intent Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  
MethodCall Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  
MethodChannel Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  NonNull Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Result Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  String Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Unit Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Uri Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  lastAuthorizationRequestResult Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  (triggerMainActivityToHideChromeCustomTab Mcom.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin.Companion  Log 
io.flutter  e io.flutter.Log  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  equals -io.flutter.plugin.common.MethodChannel.Result  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  CustomTabsIntent 	java.lang  Intent 	java.lang  Log 	java.lang  
MethodChannel 	java.lang  SignInWithApplePlugin 	java.lang  TAG 	java.lang  Uri 	java.lang  lastAuthorizationRequestResult 	java.lang  (triggerMainActivityToHideChromeCustomTab 	java.lang  Any kotlin  Boolean kotlin  CustomTabsIntent kotlin  	Function0 kotlin  Int kotlin  Intent kotlin  Log kotlin  
MethodChannel kotlin  Nothing kotlin  SignInWithApplePlugin kotlin  String kotlin  TAG kotlin  Unit kotlin  Uri kotlin  lastAuthorizationRequestResult kotlin  (triggerMainActivityToHideChromeCustomTab kotlin  CustomTabsIntent kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  
MethodChannel kotlin.annotation  SignInWithApplePlugin kotlin.annotation  TAG kotlin.annotation  Uri kotlin.annotation  lastAuthorizationRequestResult kotlin.annotation  (triggerMainActivityToHideChromeCustomTab kotlin.annotation  CustomTabsIntent kotlin.collections  Intent kotlin.collections  Log kotlin.collections  
MethodChannel kotlin.collections  SignInWithApplePlugin kotlin.collections  TAG kotlin.collections  Uri kotlin.collections  lastAuthorizationRequestResult kotlin.collections  (triggerMainActivityToHideChromeCustomTab kotlin.collections  CustomTabsIntent kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  
MethodChannel kotlin.comparisons  SignInWithApplePlugin kotlin.comparisons  TAG kotlin.comparisons  Uri kotlin.comparisons  lastAuthorizationRequestResult kotlin.comparisons  (triggerMainActivityToHideChromeCustomTab kotlin.comparisons  CustomTabsIntent 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  
MethodChannel 	kotlin.io  SignInWithApplePlugin 	kotlin.io  TAG 	kotlin.io  Uri 	kotlin.io  lastAuthorizationRequestResult 	kotlin.io  (triggerMainActivityToHideChromeCustomTab 	kotlin.io  CustomTabsIntent 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  
MethodChannel 
kotlin.jvm  SignInWithApplePlugin 
kotlin.jvm  TAG 
kotlin.jvm  Uri 
kotlin.jvm  lastAuthorizationRequestResult 
kotlin.jvm  (triggerMainActivityToHideChromeCustomTab 
kotlin.jvm  CustomTabsIntent 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  
MethodChannel 
kotlin.ranges  SignInWithApplePlugin 
kotlin.ranges  TAG 
kotlin.ranges  Uri 
kotlin.ranges  lastAuthorizationRequestResult 
kotlin.ranges  (triggerMainActivityToHideChromeCustomTab 
kotlin.ranges  CustomTabsIntent kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  
MethodChannel kotlin.sequences  SignInWithApplePlugin kotlin.sequences  TAG kotlin.sequences  Uri kotlin.sequences  lastAuthorizationRequestResult kotlin.sequences  (triggerMainActivityToHideChromeCustomTab kotlin.sequences  CustomTabsIntent kotlin.text  Intent kotlin.text  Log kotlin.text  
MethodChannel kotlin.text  SignInWithApplePlugin kotlin.text  TAG kotlin.text  Uri kotlin.text  lastAuthorizationRequestResult kotlin.text  (triggerMainActivityToHideChromeCustomTab kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
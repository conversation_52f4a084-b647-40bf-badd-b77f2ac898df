import 'package:flutter/material.dart';
import '../../features/auth/services/auth_service.dart';
import '../../shared/models/user.dart';
import '../../features/auth/pages/user_type_selection_page.dart';
import '../../features/proche/pages/proche_home_page.dart';
import '../../features/aveugle/pages/aveugle_home_page.dart';
import '../../homepage.dart';

/// Gestionnaire de routage conditionnel selon le type d'utilisateur
class AppRouter {
  static final AppRouter _instance = AppRouter._internal();
  factory AppRouter() => _instance;
  AppRouter._internal();

  final AuthService _authService = AuthService();

  /// Obtient la page d'accueil appropriée selon le type d'utilisateur
  Widget getHomePage() {
    if (!_authService.isLoggedIn) {
      return const UserTypeSelectionPage();
    }

    final user = _authService.currentUser!;
    
    switch (user.userType) {
      case UserType.proche:
        return const ProcheHomePage();
      case UserType.aveugle:
        return const AveugleHomePage();
    }
  }

  /// Vérifie si l'utilisateur peut accéder à une page
  bool canAccessPage(String pageName) {
    return _authService.canAccessRoute('/$pageName');
  }

  /// Redirige vers la page appropriée selon le type d'utilisateur
  void redirectToUserHome(BuildContext context) {
    if (!_authService.isLoggedIn) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const UserTypeSelectionPage()),
        (route) => false,
      );
      return;
    }

    final user = _authService.currentUser!;
    Widget homePage;

    switch (user.userType) {
      case UserType.proche:
        homePage = const ProcheHomePage();
        break;
      case UserType.aveugle:
        homePage = const AveugleHomePage();
        break;
    }

    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => homePage),
      (route) => false,
    );
  }

  /// Obtient les pages de navigation selon le type d'utilisateur
  List<NavigationItem> getNavigationItems() {
    if (!_authService.isLoggedIn) return [];

    final user = _authService.currentUser!;

    switch (user.userType) {
      case UserType.proche:
        return _getProcheNavigationItems();
      case UserType.aveugle:
        return _getAveugleNavigationItems();
    }
  }

  List<NavigationItem> _getProcheNavigationItems() {
    return [
      NavigationItem(
        icon: Icons.dashboard,
        label: 'Tableau de bord',
        page: const ProcheHomePage(),
      ),
      NavigationItem(
        icon: Icons.location_on,
        label: 'Localisation',
        page: const LocationTrackingPage(),
      ),
      NavigationItem(
        icon: Icons.phone,
        label: 'Appels',
        page: const CallsPage(),
      ),
      NavigationItem(
        icon: Icons.message,
        label: 'Messages',
        page: const MessagesPage(),
      ),
      NavigationItem(
        icon: Icons.health_and_safety,
        label: 'Santé',
        page: const HealthMonitoringPage(),
      ),
    ];
  }

  List<NavigationItem> _getAveugleNavigationItems() {
    return [
      NavigationItem(
        icon: Icons.home,
        label: 'Accueil',
        page: const AveugleHomePage(),
      ),
      NavigationItem(
        icon: Icons.sos,
        label: 'SOS',
        page: const SOSPage(),
      ),
      NavigationItem(
        icon: Icons.phone,
        label: 'Appels',
        page: const CallsPage(),
      ),
      NavigationItem(
        icon: Icons.message,
        label: 'Messages',
        page: const MessagesPage(),
      ),
      NavigationItem(
        icon: Icons.history,
        label: 'Historique',
        page: const HistoryPage(),
      ),
    ];
  }
}

/// Élément de navigation
class NavigationItem {
  final IconData icon;
  final String label;
  final Widget page;

  NavigationItem({
    required this.icon,
    required this.label,
    required this.page,
  });
}

// Pages temporaires - à remplacer par les vraies pages
class ProcheHomePage extends StatelessWidget {
  const ProcheHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord - Proche'),
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.dashboard, size: 80, color: Color(0xFFFF7900)),
            SizedBox(height: 20),
            Text(
              'Interface Proche/Aidant',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'Surveillez et assistez votre proche',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

class AveugleHomePage extends StatelessWidget {
  const AveugleHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const HomePage(); // Utilise l'interface existante optimisée pour les malvoyants
  }
}

// Pages placeholder
class LocationTrackingPage extends StatelessWidget {
  const LocationTrackingPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Suivi de localisation')));
}

class CallsPage extends StatelessWidget {
  const CallsPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Appels')));
}

class MessagesPage extends StatelessWidget {
  const MessagesPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Messages')));
}

class HealthMonitoringPage extends StatelessWidget {
  const HealthMonitoringPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Surveillance santé')));
}

class SOSPage extends StatelessWidget {
  const SOSPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('SOS')));
}

class HistoryPage extends StatelessWidget {
  const HistoryPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Historique')));
}

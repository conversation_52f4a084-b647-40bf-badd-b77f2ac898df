-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:24:5-55:19
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b15ef0f6dbe0ca128de5d19f1a0947f\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b15ef0f6dbe0ca128de5d19f1a0947f\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ccc9e08f97c41d72453742cfc880b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ccc9e08f97c41d72453742cfc880b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ce35238083783fe2f3a794b0da222893\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ce35238083783fe2f3a794b0da222893\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a36f837c0927b6006e94b209adca6fef\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a36f837c0927b6006e94b209adca6fef\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95e92f3d5437c888780fbbf0173ff29\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95e92f3d5437c888780fbbf0173ff29\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ec6c9a870ba9ec61f173f78402ab089\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ec6c9a870ba9ec61f173f78402ab089\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\92f5938bf97c30ea9c1f3a9cda475ac2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\92f5938bf97c30ea9c1f3a9cda475ac2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:1:1-67:12
MERGED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:1:1-67:12
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_tts] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:record_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:speech_to_text] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:battery_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sensors_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b6fe525ed9940fc78e786c4e219c431\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87ded8fbc653af9c268793e80c02e2da\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b15ef0f6dbe0ca128de5d19f1a0947f\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\66e3b130102322386e3624905495b6d1\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f9b0ee91d843a921e065f9a03ac354a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cff47270d2f7e5dae11e4893d49209a4\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f3fcaff53629259f7dcb1b56b2a8ba49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35c726d6035417b88dbdf00045d2002f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d963034925319462e2421e7516ca6b6d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3f1f5c1cc742a710f9658d4750f877e6\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4cdd0dfed21aea6022128bc9f4b82c3e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b0e22e21a40bb7559e36a7383c48713\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ccc9e08f97c41d72453742cfc880b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ce35238083783fe2f3a794b0da222893\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\107284c5b2b3b9570fd85e82382f6a92\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f04417df25b848a42fac0e6739019b4b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a36f837c0927b6006e94b209adca6fef\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95e92f3d5437c888780fbbf0173ff29\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2f5c20b5abdfce173069b1050a1a03e0\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dca308ff87485ae9315cd5c50542caff\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e27fd7814c5b0ca8667ce8284c3e8122\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4d90668ba93a2c8c1427cfb505a1beb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\522b503f68995c7461481b642e7796d4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6de330f06f374fb919f29061c04a2fd2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ace1ea951e6dfd53a5f9198b4f8ff3b4\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0012213426034c10690d70ce24a4c38\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90b441aed47b1d4b73898279c34fafb0\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\157264c963a2fc21ceeb171820339d13\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d02b16102734164ce86d9f07007dafae\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7358301bf7565e075c543cf7e4d8192\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb0cafc4ea8caf1075e385424f9de06b\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b3666df027932a7f234b2feec20044e\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8a5fa42ec34cbc21dbe939c8713e27d7\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ec6c9a870ba9ec61f173f78402ab089\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\543662009289cf98b732cf524a6d6d9a\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\43bf1a4ab34f7d27c57ace34278b4a30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3821d541e6d9512cce93da84f107a7f2\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ae1e32cdfb092a64a75bb864c5be5c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\770ad2685e99b2afb10979277791214a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f4ee0cb27c08bb6d49131ac96cbaab1b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ee0c755deaaac00c01c2e7941443d9b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\229e7ade28929990a20f6714560a32ed\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\15edb5554bfcd7c7414f8416b8af28f0\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c40af4e93eccd58f736d9dd0dc50f6f\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27d5650f5118bca2aee30d93a04b6674\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91a083e7c77fd72b1b9d060fb51103e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7435f50f9b3d10045276cc3fc2d46857\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b9afc05e451c59d46b06ed4dd4d1f83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77a019c7997816fc429b2f7413189511\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb767ce46b249af0de5d9989a5f34fee\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ed9d183fa2cde4c6b100b79330bda39\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\60ef80fc6fbe033450e8e9734149a4bc\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1786d211569345505fe2319592f2a103\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ce98d32f56343e62290913247e61bb8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\92f5938bf97c30ea9c1f3a9cda475ac2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f12fec9531ecbbee819b72ca65aaab54\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d875bb4b3d02089a33aae9ce06ef37e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c441dcd467b2f84a712b8289712f995\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\955f8d46812686371ae1dc5cfa8365ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c208543e5fbb089cb6925029ac5e26f3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8bedca82d8a9c65ebb1419e7278f8e12\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\859174345d04bda9078408e47a38ac6f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed536e7fd829deae5ac94b7c090c9d6c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:2:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:2:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:3:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:3:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:4:5-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:4:22-82
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:5:5-64
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:5:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:6:5-70
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
MERGED from [:record_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-71
MERGED from [:record_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:7:5-80
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
	android:maxSdkVersion
		ADDED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:5-66
MERGED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:5-66
MERGED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:5-66
MERGED from [:google_sign_in_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:9:22-64
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:12:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:12:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:13:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:13:22-70
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:14:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:14:22-66
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:17:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:17:22-64
uses-permission#android.permission.SEND_SMS
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:18:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:18:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:19:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:19:22-67
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:22:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:22:22-69
uses-permission#android.permission.WRITE_CONTACTS
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:23:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:23:22-70
queries
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:61:5-66:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:62:9-65:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:63:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:63:21-70
data
ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\main\AndroidManifest.xml:64:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_tts] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_tts] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:record_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:record_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:battery_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:battery_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b6fe525ed9940fc78e786c4e219c431\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b6fe525ed9940fc78e786c4e219c431\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87ded8fbc653af9c268793e80c02e2da\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87ded8fbc653af9c268793e80c02e2da\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b15ef0f6dbe0ca128de5d19f1a0947f\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b15ef0f6dbe0ca128de5d19f1a0947f\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\66e3b130102322386e3624905495b6d1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\66e3b130102322386e3624905495b6d1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f9b0ee91d843a921e065f9a03ac354a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f9b0ee91d843a921e065f9a03ac354a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cff47270d2f7e5dae11e4893d49209a4\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cff47270d2f7e5dae11e4893d49209a4\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f3fcaff53629259f7dcb1b56b2a8ba49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f3fcaff53629259f7dcb1b56b2a8ba49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35c726d6035417b88dbdf00045d2002f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35c726d6035417b88dbdf00045d2002f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d963034925319462e2421e7516ca6b6d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d963034925319462e2421e7516ca6b6d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3f1f5c1cc742a710f9658d4750f877e6\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3f1f5c1cc742a710f9658d4750f877e6\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4cdd0dfed21aea6022128bc9f4b82c3e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4cdd0dfed21aea6022128bc9f4b82c3e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b0e22e21a40bb7559e36a7383c48713\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3b0e22e21a40bb7559e36a7383c48713\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ccc9e08f97c41d72453742cfc880b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d97ccc9e08f97c41d72453742cfc880b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ce35238083783fe2f3a794b0da222893\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ce35238083783fe2f3a794b0da222893\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\107284c5b2b3b9570fd85e82382f6a92\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\107284c5b2b3b9570fd85e82382f6a92\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f04417df25b848a42fac0e6739019b4b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f04417df25b848a42fac0e6739019b4b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a36f837c0927b6006e94b209adca6fef\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a36f837c0927b6006e94b209adca6fef\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95e92f3d5437c888780fbbf0173ff29\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f95e92f3d5437c888780fbbf0173ff29\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2f5c20b5abdfce173069b1050a1a03e0\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2f5c20b5abdfce173069b1050a1a03e0\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dca308ff87485ae9315cd5c50542caff\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dca308ff87485ae9315cd5c50542caff\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e27fd7814c5b0ca8667ce8284c3e8122\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e27fd7814c5b0ca8667ce8284c3e8122\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4d90668ba93a2c8c1427cfb505a1beb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4d90668ba93a2c8c1427cfb505a1beb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\522b503f68995c7461481b642e7796d4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\522b503f68995c7461481b642e7796d4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6de330f06f374fb919f29061c04a2fd2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6de330f06f374fb919f29061c04a2fd2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ace1ea951e6dfd53a5f9198b4f8ff3b4\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ace1ea951e6dfd53a5f9198b4f8ff3b4\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0012213426034c10690d70ce24a4c38\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0012213426034c10690d70ce24a4c38\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90b441aed47b1d4b73898279c34fafb0\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90b441aed47b1d4b73898279c34fafb0\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\157264c963a2fc21ceeb171820339d13\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\157264c963a2fc21ceeb171820339d13\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d02b16102734164ce86d9f07007dafae\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d02b16102734164ce86d9f07007dafae\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7358301bf7565e075c543cf7e4d8192\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d7358301bf7565e075c543cf7e4d8192\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb0cafc4ea8caf1075e385424f9de06b\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb0cafc4ea8caf1075e385424f9de06b\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b3666df027932a7f234b2feec20044e\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b3666df027932a7f234b2feec20044e\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8a5fa42ec34cbc21dbe939c8713e27d7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8a5fa42ec34cbc21dbe939c8713e27d7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ec6c9a870ba9ec61f173f78402ab089\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ec6c9a870ba9ec61f173f78402ab089\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\543662009289cf98b732cf524a6d6d9a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\543662009289cf98b732cf524a6d6d9a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\43bf1a4ab34f7d27c57ace34278b4a30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\43bf1a4ab34f7d27c57ace34278b4a30\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3821d541e6d9512cce93da84f107a7f2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3821d541e6d9512cce93da84f107a7f2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ae1e32cdfb092a64a75bb864c5be5c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ae1e32cdfb092a64a75bb864c5be5c\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\770ad2685e99b2afb10979277791214a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\770ad2685e99b2afb10979277791214a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f4ee0cb27c08bb6d49131ac96cbaab1b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f4ee0cb27c08bb6d49131ac96cbaab1b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ee0c755deaaac00c01c2e7941443d9b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ee0c755deaaac00c01c2e7941443d9b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\229e7ade28929990a20f6714560a32ed\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\229e7ade28929990a20f6714560a32ed\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\15edb5554bfcd7c7414f8416b8af28f0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\15edb5554bfcd7c7414f8416b8af28f0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c40af4e93eccd58f736d9dd0dc50f6f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c40af4e93eccd58f736d9dd0dc50f6f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27d5650f5118bca2aee30d93a04b6674\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27d5650f5118bca2aee30d93a04b6674\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91a083e7c77fd72b1b9d060fb51103e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91a083e7c77fd72b1b9d060fb51103e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7435f50f9b3d10045276cc3fc2d46857\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7435f50f9b3d10045276cc3fc2d46857\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b9afc05e451c59d46b06ed4dd4d1f83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b9afc05e451c59d46b06ed4dd4d1f83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77a019c7997816fc429b2f7413189511\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77a019c7997816fc429b2f7413189511\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb767ce46b249af0de5d9989a5f34fee\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb767ce46b249af0de5d9989a5f34fee\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ed9d183fa2cde4c6b100b79330bda39\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ed9d183fa2cde4c6b100b79330bda39\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\60ef80fc6fbe033450e8e9734149a4bc\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\60ef80fc6fbe033450e8e9734149a4bc\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1786d211569345505fe2319592f2a103\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1786d211569345505fe2319592f2a103\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ce98d32f56343e62290913247e61bb8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ce98d32f56343e62290913247e61bb8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\92f5938bf97c30ea9c1f3a9cda475ac2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\92f5938bf97c30ea9c1f3a9cda475ac2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f12fec9531ecbbee819b72ca65aaab54\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f12fec9531ecbbee819b72ca65aaab54\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d875bb4b3d02089a33aae9ce06ef37e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d875bb4b3d02089a33aae9ce06ef37e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c441dcd467b2f84a712b8289712f995\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c441dcd467b2f84a712b8289712f995\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\955f8d46812686371ae1dc5cfa8365ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\955f8d46812686371ae1dc5cfa8365ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c208543e5fbb089cb6925029ac5e26f3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c208543e5fbb089cb6925029ac5e26f3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8bedca82d8a9c65ebb1419e7278f8e12\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8bedca82d8a9c65ebb1419e7278f8e12\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\859174345d04bda9078408e47a38ac6f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\859174345d04bda9078408e47a38ac6f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed536e7fd829deae5ac94b7c090c9d6c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed536e7fd829deae5ac94b7c090c9d6c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\android\app\src\debug\AndroidManifest.xml
uses-feature#android.hardware.camera.any
ADDED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
	android:name
		ADDED from [:camera_android_camerax] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6106887c341f56946a10814271e50d75\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\983e4dbdadfbfc04975f76c72feb6d55\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [:connectivity_plus] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\app_mobile\canne-connectee\app_mobile\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6d60e0483f682607580cf2c7102d13cb\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ec66158bf7b2cef13c33e3b9cc1d27c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162ce9f4799f4d70ac53979cc1edc061\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0135ff39089948e8e1d312c122d7a350\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\89e8540cf1281823a32debdfceb8da1e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\36e3ece47fbd740e857b13424e8c2994\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a27b6c07017f710011acb43e72ed143b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\750760934eaadc52a104796ebe6e861c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\50f6926fbb4123f341453b52d846d408\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d016860d338ead82bf1026ad9daf8f0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6f414d0e13fd60c8585a8bd22c5c261e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.canne_connectee.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.canne_connectee.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dd8303f18c83ea4858ce6d157a244f33\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a5624d00312f14f23800cb93cb890584\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9f20dbe566a3d2aa0463bddb20b72312\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93

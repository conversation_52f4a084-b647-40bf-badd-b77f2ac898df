import 'package:flutter/material.dart';
import 'package:canne_connectee/features/auth/widgets/user_type_router.dart';
import 'package:canne_connectee/features/auth/widgets/auth_wrapper.dart';
import 'package:camera/camera.dart';
import 'core/app_initializer.dart';
import 'core/logging/app_logger.dart';

List<CameraDescription> cameras = [];

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialiser l'application avec tous les services
    await AppInitializer().initialize();

    // Initialiser les caméras en arrière-plan
    _initializeCamerasInBackground();

    runApp(const MyApp());
  } catch (e) {
    final logger = AppLogger.getLogger('Main');
    logger.severe('❌ Erreur critique au démarrage: $e', e);

    // Afficher une application d'erreur minimale
    runApp(ErrorApp(error: e.toString()));
  }
}

void _initializeCamerasInBackground() {
  final logger = AppLogger.getLogger('CameraInit');

  Future.delayed(Duration.zero, () async {
    try {
      cameras = await availableCameras();
      logger.info('📷 Caméras initialisées: ${cameras.length} caméras trouvées');
    } catch (e) {
      cameras = [];
      logger.warning('📷 Erreur initialisation caméras: $e');
    }
  });
}

/// Application d'erreur minimale en cas d'échec critique
class ErrorApp extends StatelessWidget {
  final String error;

  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Erreur - Canne Connectée',
      home: Scaffold(
        backgroundColor: Colors.red[50],
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 20),
                const Text(
                  'Erreur de démarrage',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'L\'application n\'a pas pu démarrer correctement.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.red[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    error,
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // Redémarrer l'application
                    main();
                  },
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Canne Smart',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFFFF7900)),
        useMaterial3: true,
      ),

      home: const AuthWrapper(
        child: UserTypeRouter(),
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

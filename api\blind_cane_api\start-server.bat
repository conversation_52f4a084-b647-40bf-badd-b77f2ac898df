@echo off
echo 🚀 Démarrage du serveur API Canne Connectée...
echo.

cd /d "C:\Users\<USER>\Desktop\app_mobile\canne-connectee\api\blind_cane_api"

echo 📁 Répertoire actuel: %CD%
echo.

echo 📦 Vérification des modules Node.js...
if not exist "node_modules" (
    echo ❌ node_modules non trouvé, installation des dépendances...
    npm install
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
) else (
    echo ✅ node_modules trouvé
)

echo.
echo 🌟 Démarrage du serveur...
node server.js

pause

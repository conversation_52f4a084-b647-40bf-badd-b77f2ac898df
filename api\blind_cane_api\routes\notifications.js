const express = require('express');
const { body, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/notifications
 * @desc Récupère la liste des notifications
 */
router.get('/', authMiddleware, [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('type').optional().isIn(['alerte', 'message', 'appel', 'systeme', 'urgence']).withMessage('Type invalide'),
  query('read').optional().isBoolean().withMessage('Read doit être un booléen'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const { user_id, type, read, limit = 50, offset = 0 } = req.query;

    // Simulation de données de notifications
    const notifications = [
      {
        id: 1,
        user_id: 1,
        type: 'alerte',
        titre: 'Alerte SOS reçue',
        message: 'Une alerte SOS a été déclenchée par Marie Dupont',
        data: {
          alerte_id: 123,
          contact_nom: 'Marie Dupont',
          localisation: 'Paris 15ème'
        },
        read: false,
        priority: 'high',
        date_creation: new Date(Date.now() - 300000).toISOString(), // il y a 5 min
        date_lecture: null
      },
      {
        id: 2,
        user_id: 1,
        type: 'message',
        titre: 'Nouveau message',
        message: 'Vous avez reçu un nouveau message de Pierre Martin',
        data: {
          message_id: 456,
          expediteur: 'Pierre Martin',
          apercu: 'Bonjour, comment allez-vous ?'
        },
        read: true,
        priority: 'normal',
        date_creation: new Date(Date.now() - 1800000).toISOString(), // il y a 30 min
        date_lecture: new Date(Date.now() - 1200000).toISOString() // lu il y a 20 min
      },
      {
        id: 3,
        user_id: 1,
        type: 'appel',
        titre: 'Appel manqué',
        message: 'Appel manqué de Dr. Leblanc',
        data: {
          appel_id: 789,
          appelant: 'Dr. Leblanc',
          numero: '+33555666777'
        },
        read: false,
        priority: 'normal',
        date_creation: new Date(Date.now() - 3600000).toISOString(), // il y a 1h
        date_lecture: null
      },
      {
        id: 4,
        user_id: 1,
        type: 'systeme',
        titre: 'Mise à jour disponible',
        message: 'Une nouvelle version de l\'application est disponible',
        data: {
          version: '2.1.0',
          taille: '15.2 MB'
        },
        read: true,
        priority: 'low',
        date_creation: new Date(Date.now() - 86400000).toISOString(), // il y a 1 jour
        date_lecture: new Date(Date.now() - 82800000).toISOString() // lu il y a 23h
      }
    ];

    let filteredNotifications = notifications;
    
    if (user_id) {
      filteredNotifications = filteredNotifications.filter(notif => notif.user_id === parseInt(user_id));
    }

    if (type) {
      filteredNotifications = filteredNotifications.filter(notif => notif.type === type);
    }

    if (read !== undefined) {
      const isRead = read === 'true';
      filteredNotifications = filteredNotifications.filter(notif => notif.read === isRead);
    }

    // Trier par date de création (plus récent en premier)
    filteredNotifications.sort((a, b) => new Date(b.date_creation) - new Date(a.date_creation));

    const paginatedNotifications = filteredNotifications.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

    res.json({
      notifications: paginatedNotifications,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: filteredNotifications.length
      },
      summary: {
        total: filteredNotifications.length,
        unread: filteredNotifications.filter(n => !n.read).length,
        high_priority: filteredNotifications.filter(n => n.priority === 'high').length
      }
    });

  } catch (error) {
    console.error('Erreur récupération notifications:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des notifications',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/notifications
 * @desc Crée une nouvelle notification
 */
router.post('/', authMiddleware, [
  body('user_id')
    .isInt()
    .withMessage('ID utilisateur invalide'),
  
  body('type')
    .isIn(['alerte', 'message', 'appel', 'systeme', 'urgence'])
    .withMessage('Type de notification invalide'),
  
  body('titre')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Le titre doit contenir entre 1 et 200 caractères'),
  
  body('message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Le message doit contenir entre 1 et 1000 caractères'),
  
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Priorité invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { user_id, type, titre, message, data = {}, priority = 'normal' } = req.body;

    const newNotification = {
      id: Date.now(),
      user_id: parseInt(user_id),
      type,
      titre,
      message,
      data,
      read: false,
      priority,
      date_creation: new Date().toISOString(),
      date_lecture: null
    };

    res.status(201).json({
      message: 'Notification créée avec succès',
      notification: newNotification
    });

  } catch (error) {
    console.error('Erreur création notification:', error);
    res.status(500).json({
      error: 'Erreur lors de la création de la notification',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/notifications/:id
 * @desc Récupère une notification spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const notificationId = parseInt(req.params.id);
    
    if (isNaN(notificationId)) {
      return res.status(400).json({
        error: 'ID notification invalide'
      });
    }

    // Simulation de récupération de notification
    const notification = {
      id: notificationId,
      user_id: 1,
      type: 'alerte',
      titre: 'Alerte SOS reçue',
      message: 'Une alerte SOS a été déclenchée par Marie Dupont',
      data: {
        alerte_id: 123,
        contact_nom: 'Marie Dupont',
        localisation: 'Paris 15ème'
      },
      read: false,
      priority: 'high',
      date_creation: new Date().toISOString(),
      date_lecture: null
    };

    res.json({
      notification
    });

  } catch (error) {
    console.error('Erreur récupération notification:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération de la notification',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/notifications/:id/read
 * @desc Marque une notification comme lue
 */
router.put('/:id/read', authMiddleware, async (req, res) => {
  try {
    const notificationId = parseInt(req.params.id);
    
    if (isNaN(notificationId)) {
      return res.status(400).json({
        error: 'ID notification invalide'
      });
    }

    // Simulation de marquage comme lue
    const updatedNotification = {
      id: notificationId,
      read: true,
      date_lecture: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.json({
      message: 'Notification marquée comme lue',
      notification: updatedNotification
    });

  } catch (error) {
    console.error('Erreur marquage notification lue:', error);
    res.status(500).json({
      error: 'Erreur lors du marquage de la notification',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/notifications/bulk-read
 * @desc Marque plusieurs notifications comme lues
 */
router.put('/bulk-read', authMiddleware, [
  body('notification_ids')
    .isArray()
    .withMessage('notification_ids doit être un tableau'),
  
  body('notification_ids.*')
    .isInt()
    .withMessage('Chaque ID de notification doit être un entier')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { notification_ids } = req.body;

    // Simulation de marquage en masse
    const updatedCount = notification_ids.length;

    res.json({
      message: `${updatedCount} notifications marquées comme lues`,
      updated_count: updatedCount,
      updated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erreur marquage en masse notifications:', error);
    res.status(500).json({
      error: 'Erreur lors du marquage en masse',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/notifications/:id
 * @desc Supprime une notification
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const notificationId = parseInt(req.params.id);
    
    if (isNaN(notificationId)) {
      return res.status(400).json({
        error: 'ID notification invalide'
      });
    }

    res.json({
      message: 'Notification supprimée avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression notification:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression de la notification',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

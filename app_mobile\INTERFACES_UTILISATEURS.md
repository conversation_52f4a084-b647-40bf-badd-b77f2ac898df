# Interfaces Utilisateurs - Canne Connectée

## Vue d'ensemble

L'application Canne Connectée propose maintenant deux interfaces distinctes selon le type d'utilisateur :

### 🦯 Interface Aveugle/Malvoyant (Simplifiée)
- **3 vues principales uniquement** : Accueil, <PERSON><PERSON><PERSON>, Messages
- Interface épurée et accessible
- Navigation vocale intégrée
- Commandes vocales simplifiées

### 👥 Interface Proche/Aidant (Complète)
- **4 vues principales** : Tableau de bord, Position temps réel, Historique SOS, Historique trajets
- Interface riche avec données détaillées
- Suivi en temps réel
- Historiques complets

---

## 🦯 Interface Aveugle - Fonctionnalités

### Navigation Simplifiée
- **Accueil** : Vue principale avec informations essentielles
- **Appels** : Gestion des appels téléphoniques
- **Messages** : Gestion des SMS et messages

### Commandes Vocales (7 commandes principales)
1. **"accueil"** ou **"home"** → Aller à l'accueil
2. **"appels"** ou **"téléphone"** → Aller aux appels
3. **"messages"** ou **"sms"** → Aller aux messages
4. **"heure"** ou **"quelle heure"** → Annoncer l'heure
5. **"aide"** → Afficher l'aide vocale
6. **"test"** → Test de la synthèse vocale
7. **"urgence"** → Déclencher une alerte SOS

### Caractéristiques d'Accessibilité
- **Bouton microphone animé** avec effets visuels
- **Feedback vocal** pour toutes les actions
- **Navigation tactile** simplifiée
- **Icônes grandes** et contrastées
- **Synthèse vocale** intégrée

---

## 👥 Interface Proche - Fonctionnalités

### 1. 📊 Tableau de Bord
- **Statut général** de l'aveugle (sécurité, dernière activité)
- **Accès rapide** aux fonctions principales
- **Dernière position** connue avec détails
- **Activités récentes** (trajets, appels, messages)
- **Statistiques** de la batterie de la canne

### 2. 📍 Position Temps Réel
- **Carte interactive** avec position actuelle
- **Informations détaillées** :
  - Adresse exacte
  - Coordonnées GPS
  - Précision du signal
  - Vitesse de déplacement
- **Contrôles** :
  - Actualisation manuelle
  - Centrage automatique
  - Partage de position
  - Options de suivi

### 3. 🚨 Historique SOS
- **Liste complète** des alertes SOS
- **Filtrage** par urgence et statut :
  - Toutes les alertes
  - Critiques uniquement
  - Urgentes uniquement
  - Modérées uniquement
  - Résolues uniquement
- **Détails complets** pour chaque alerte :
  - Type d'urgence (Médicale, Chute, Assistance)
  - Message détaillé
  - Niveau d'urgence
  - Localisation précise
  - Temps de réponse
  - Statut de résolution

### 4. 🛣️ Historique des Trajets
- **Organisation par jour** (Aujourd'hui, Hier, etc.)
- **Statistiques globales** :
  - Nombre total de trajets
  - Distance totale parcourue
  - Temps total de déplacement
- **Détails par trajet** :
  - Heure de départ/arrivée
  - Lieux de départ/destination
  - Distance parcourue
  - Durée du trajet
  - Vitesse moyenne/maximale
  - Arrêts effectués

#### 🗺️ BottomSheet Détaillé (Trajet)
Quand on clique sur un trajet, un **BottomSheet** s'ouvre avec :

- **Carte interactive** montrant :
  - Tracé complet du trajet
  - Point de départ (vert)
  - Point d'arrivée (rouge)
  - Arrêts intermédiaires (orange)
  
- **Informations complètes** :
  - Horaires précis
  - Statistiques de vitesse
  - Détails des arrêts
  - Coordonnées GPS

---

## 🔄 Système de Routage

### UserTypeRouter
Le système détermine automatiquement quelle interface afficher :

```dart
// Selon le type d'utilisateur connecté
switch (userType) {
  case UserType.aveugle:
    return AveuglesHomePage(); // Interface simplifiée
  case UserType.proche:
    return ProcheHomePage();   // Interface complète
}
```

### Authentification
- **Connexion classique** (email/mot de passe)
- **Authentification sociale** :
  - Google Sign-In
  - Apple Sign-In (iOS uniquement)
  - Facebook (à venir)
  - Microsoft (à venir)

---

## 📱 Navigation et UX

### Interface Aveugle
- **BottomNavigationBar** avec 3 onglets
- **FloatingActionButton** pour commandes vocales
- **Drawer** avec options d'aide et déconnexion
- **Animations** visuelles pour le feedback
- **Sons** et vibrations pour l'accessibilité

### Interface Proche
- **BottomNavigationBar** avec 4 onglets
- **AppBar** avec notifications et urgences
- **Drawer** complet avec paramètres
- **Cartes interactives** avec zoom/pan
- **BottomSheets** pour détails étendus

---

## 🎨 Design System

### Couleurs Principales
- **Orange Principal** : `#FF7900`
- **Orange Foncé** : `#E66A00`
- **Arrière-plan** : `#F5F5F5`
- **Blanc** : `#FFFFFF`

### Typographie
- **Titres** : Bold, 18-24px
- **Corps** : Regular, 14-16px
- **Détails** : 12px
- **Accessibilité** : Tailles augmentées pour l'interface aveugle

### Icônes
- **Material Icons** pour la cohérence
- **Tailles adaptées** selon l'interface
- **Couleurs contrastées** pour l'accessibilité

---

## 🔧 Architecture Technique

### Structure des Dossiers
```
lib/
├── features/
│   ├── aveugle/
│   │   └── pages/
│   │       └── aveugle_home_page.dart
│   ├── proche/
│   │   ├── pages/
│   │   │   ├── proche_home_page.dart
│   │   │   ├── real_time_tracking_page.dart
│   │   │   ├── sos_history_page.dart
│   │   │   └── journey_history_page.dart
│   │   └── widgets/
│   │       └── proche_dashboard_widget.dart
│   └── auth/
│       └── widgets/
│           └── user_type_router.dart
```

### Services Utilisés
- **AuthService** : Gestion de l'authentification
- **LocationService** : Géolocalisation
- **VoiceCommandService** : Commandes vocales
- **TextToSpeechService** : Synthèse vocale
- **NotificationService** : Notifications push

---

## 🚀 Prochaines Étapes

### Améliorations Prévues
1. **Synchronisation temps réel** entre les interfaces
2. **Notifications push** pour les alertes SOS
3. **Géofencing** pour les zones de sécurité
4. **Analyse prédictive** des trajets
5. **Intégration IoT** avec la canne connectée

### Fonctionnalités à Développer
- **Chat en temps réel** entre aveugle et proche
- **Partage de localisation** temporaire
- **Contacts d'urgence** multiples
- **Rapports périodiques** d'activité
- **Mode hors ligne** avec synchronisation

---

## 📞 Support et Documentation

Pour plus d'informations sur l'utilisation des interfaces :
- Consultez l'aide intégrée dans chaque application
- Utilisez les commandes vocales "aide" dans l'interface aveugle
- Accédez aux paramètres via le drawer de navigation

L'application s'adapte automatiquement au type d'utilisateur connecté pour offrir l'expérience la plus appropriée à chaque profil d'usage.

  Context android.content  SENSOR_SERVICE android.content.Context  getSystemService android.content.Context  Sensor android.hardware  SensorEvent android.hardware  SensorEventListener android.hardware  
SensorManager android.hardware  TYPE_ACCELEROMETER android.hardware.Sensor  TYPE_GYROSCOPE android.hardware.Sensor  TYPE_LINEAR_ACCELERATION android.hardware.Sensor  TYPE_MAGNETIC_FIELD android.hardware.Sensor  equals android.hardware.Sensor  values android.hardware.SensorEvent  equals $android.hardware.SensorEventListener  getDefaultSensor android.hardware.SensorManager  registerListener android.hardware.SensorManager  unregisterListener android.hardware.SensorManager  ACCELEROMETER_CHANNEL_NAME !dev.fluttercommunity.plus.sensors  Any !dev.fluttercommunity.plus.sensors  Context !dev.fluttercommunity.plus.sensors  DoubleArray !dev.fluttercommunity.plus.sensors  EventChannel !dev.fluttercommunity.plus.sensors  GYROSCOPE_CHANNEL_NAME !dev.fluttercommunity.plus.sensors  Int !dev.fluttercommunity.plus.sensors  MAGNETOMETER_CHANNEL_NAME !dev.fluttercommunity.plus.sensors  METHOD_CHANNEL_NAME !dev.fluttercommunity.plus.sensors  
MethodChannel !dev.fluttercommunity.plus.sensors  Sensor !dev.fluttercommunity.plus.sensors  
SensorsPlugin !dev.fluttercommunity.plus.sensors  StreamHandlerImpl !dev.fluttercommunity.plus.sensors  String !dev.fluttercommunity.plus.sensors  USER_ACCELEROMETER_CHANNEL_NAME !dev.fluttercommunity.plus.sensors  forEachIndexed !dev.fluttercommunity.plus.sensors  ACCELEROMETER_CHANNEL_NAME /dev.fluttercommunity.plus.sensors.SensorsPlugin  BinaryMessenger /dev.fluttercommunity.plus.sensors.SensorsPlugin  Context /dev.fluttercommunity.plus.sensors.SensorsPlugin  EventChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  FlutterPluginBinding /dev.fluttercommunity.plus.sensors.SensorsPlugin  GYROSCOPE_CHANNEL_NAME /dev.fluttercommunity.plus.sensors.SensorsPlugin  Int /dev.fluttercommunity.plus.sensors.SensorsPlugin  MAGNETOMETER_CHANNEL_NAME /dev.fluttercommunity.plus.sensors.SensorsPlugin  METHOD_CHANNEL_NAME /dev.fluttercommunity.plus.sensors.SensorsPlugin  
MethodChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  Sensor /dev.fluttercommunity.plus.sensors.SensorsPlugin  
SensorManager /dev.fluttercommunity.plus.sensors.SensorsPlugin  StreamHandlerImpl /dev.fluttercommunity.plus.sensors.SensorsPlugin  USER_ACCELEROMETER_CHANNEL_NAME /dev.fluttercommunity.plus.sensors.SensorsPlugin  accelerometerChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  accelerometerStreamHandler /dev.fluttercommunity.plus.sensors.SensorsPlugin  gyroscopeChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  gyroscopeStreamHandler /dev.fluttercommunity.plus.sensors.SensorsPlugin  magnetometerChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  magnetometerStreamHandler /dev.fluttercommunity.plus.sensors.SensorsPlugin  
methodChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  setupEventChannels /dev.fluttercommunity.plus.sensors.SensorsPlugin  setupMethodChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  teardownEventChannels /dev.fluttercommunity.plus.sensors.SensorsPlugin  teardownMethodChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  userAccelChannel /dev.fluttercommunity.plus.sensors.SensorsPlugin  userAccelStreamHandler /dev.fluttercommunity.plus.sensors.SensorsPlugin  ACCELEROMETER_CHANNEL_NAME 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  BinaryMessenger 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  Context 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  EventChannel 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  FlutterPluginBinding 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  GYROSCOPE_CHANNEL_NAME 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  Int 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  MAGNETOMETER_CHANNEL_NAME 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  METHOD_CHANNEL_NAME 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  
MethodChannel 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  Sensor 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  
SensorManager 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  StreamHandlerImpl 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  USER_ACCELEROMETER_CHANNEL_NAME 9dev.fluttercommunity.plus.sensors.SensorsPlugin.Companion  Any 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  DoubleArray 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  	EventSink 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  Int 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  Sensor 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  SensorEvent 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  SensorEventListener 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  
SensorManager 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  String 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  createSensorEventListener 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  equals 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  forEachIndexed 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  getFOREachIndexed 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  getForEachIndexed 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  
getSensorName 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  onCancel 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  samplingPeriod 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  sensor 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  sensorEventListener 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  
sensorManager 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  
sensorType 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  updateRegistration 3dev.fluttercommunity.plus.sensors.StreamHandlerImpl  getFOREachIndexed `dev.fluttercommunity.plus.sensors.StreamHandlerImpl.createSensorEventListener.<no name provided>  getForEachIndexed `dev.fluttercommunity.plus.sensors.StreamHandlerImpl.createSensorEventListener.<no name provided>  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ACCELEROMETER_CHANNEL_NAME 	java.lang  Context 	java.lang  DoubleArray 	java.lang  EventChannel 	java.lang  GYROSCOPE_CHANNEL_NAME 	java.lang  MAGNETOMETER_CHANNEL_NAME 	java.lang  METHOD_CHANNEL_NAME 	java.lang  
MethodChannel 	java.lang  Sensor 	java.lang  StreamHandlerImpl 	java.lang  USER_ACCELEROMETER_CHANNEL_NAME 	java.lang  forEachIndexed 	java.lang  ACCELEROMETER_CHANNEL_NAME kotlin  Any kotlin  Boolean kotlin  Context kotlin  Double kotlin  DoubleArray kotlin  EventChannel kotlin  Float kotlin  	Function2 kotlin  GYROSCOPE_CHANNEL_NAME kotlin  Int kotlin  MAGNETOMETER_CHANNEL_NAME kotlin  METHOD_CHANNEL_NAME kotlin  
MethodChannel kotlin  Nothing kotlin  Sensor kotlin  StreamHandlerImpl kotlin  String kotlin  USER_ACCELEROMETER_CHANNEL_NAME kotlin  forEachIndexed kotlin  getFOREachIndexed kotlin.FloatArray  getForEachIndexed kotlin.FloatArray  ACCELEROMETER_CHANNEL_NAME kotlin.annotation  Context kotlin.annotation  DoubleArray kotlin.annotation  EventChannel kotlin.annotation  GYROSCOPE_CHANNEL_NAME kotlin.annotation  MAGNETOMETER_CHANNEL_NAME kotlin.annotation  METHOD_CHANNEL_NAME kotlin.annotation  
MethodChannel kotlin.annotation  Sensor kotlin.annotation  StreamHandlerImpl kotlin.annotation  USER_ACCELEROMETER_CHANNEL_NAME kotlin.annotation  forEachIndexed kotlin.annotation  ACCELEROMETER_CHANNEL_NAME kotlin.collections  Context kotlin.collections  DoubleArray kotlin.collections  EventChannel kotlin.collections  GYROSCOPE_CHANNEL_NAME kotlin.collections  MAGNETOMETER_CHANNEL_NAME kotlin.collections  METHOD_CHANNEL_NAME kotlin.collections  
MethodChannel kotlin.collections  Sensor kotlin.collections  StreamHandlerImpl kotlin.collections  USER_ACCELEROMETER_CHANNEL_NAME kotlin.collections  forEachIndexed kotlin.collections  ACCELEROMETER_CHANNEL_NAME kotlin.comparisons  Context kotlin.comparisons  DoubleArray kotlin.comparisons  EventChannel kotlin.comparisons  GYROSCOPE_CHANNEL_NAME kotlin.comparisons  MAGNETOMETER_CHANNEL_NAME kotlin.comparisons  METHOD_CHANNEL_NAME kotlin.comparisons  
MethodChannel kotlin.comparisons  Sensor kotlin.comparisons  StreamHandlerImpl kotlin.comparisons  USER_ACCELEROMETER_CHANNEL_NAME kotlin.comparisons  forEachIndexed kotlin.comparisons  ACCELEROMETER_CHANNEL_NAME 	kotlin.io  Context 	kotlin.io  DoubleArray 	kotlin.io  EventChannel 	kotlin.io  GYROSCOPE_CHANNEL_NAME 	kotlin.io  MAGNETOMETER_CHANNEL_NAME 	kotlin.io  METHOD_CHANNEL_NAME 	kotlin.io  
MethodChannel 	kotlin.io  Sensor 	kotlin.io  StreamHandlerImpl 	kotlin.io  USER_ACCELEROMETER_CHANNEL_NAME 	kotlin.io  forEachIndexed 	kotlin.io  ACCELEROMETER_CHANNEL_NAME 
kotlin.jvm  Context 
kotlin.jvm  DoubleArray 
kotlin.jvm  EventChannel 
kotlin.jvm  GYROSCOPE_CHANNEL_NAME 
kotlin.jvm  MAGNETOMETER_CHANNEL_NAME 
kotlin.jvm  METHOD_CHANNEL_NAME 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Sensor 
kotlin.jvm  StreamHandlerImpl 
kotlin.jvm  USER_ACCELEROMETER_CHANNEL_NAME 
kotlin.jvm  forEachIndexed 
kotlin.jvm  ACCELEROMETER_CHANNEL_NAME 
kotlin.ranges  Context 
kotlin.ranges  DoubleArray 
kotlin.ranges  EventChannel 
kotlin.ranges  GYROSCOPE_CHANNEL_NAME 
kotlin.ranges  MAGNETOMETER_CHANNEL_NAME 
kotlin.ranges  METHOD_CHANNEL_NAME 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Sensor 
kotlin.ranges  StreamHandlerImpl 
kotlin.ranges  USER_ACCELEROMETER_CHANNEL_NAME 
kotlin.ranges  forEachIndexed 
kotlin.ranges  ACCELEROMETER_CHANNEL_NAME kotlin.sequences  Context kotlin.sequences  DoubleArray kotlin.sequences  EventChannel kotlin.sequences  GYROSCOPE_CHANNEL_NAME kotlin.sequences  MAGNETOMETER_CHANNEL_NAME kotlin.sequences  METHOD_CHANNEL_NAME kotlin.sequences  
MethodChannel kotlin.sequences  Sensor kotlin.sequences  StreamHandlerImpl kotlin.sequences  USER_ACCELEROMETER_CHANNEL_NAME kotlin.sequences  forEachIndexed kotlin.sequences  ACCELEROMETER_CHANNEL_NAME kotlin.text  Context kotlin.text  DoubleArray kotlin.text  EventChannel kotlin.text  GYROSCOPE_CHANNEL_NAME kotlin.text  MAGNETOMETER_CHANNEL_NAME kotlin.text  METHOD_CHANNEL_NAME kotlin.text  
MethodChannel kotlin.text  Sensor kotlin.text  StreamHandlerImpl kotlin.text  USER_ACCELEROMETER_CHANNEL_NAME kotlin.text  forEachIndexed kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
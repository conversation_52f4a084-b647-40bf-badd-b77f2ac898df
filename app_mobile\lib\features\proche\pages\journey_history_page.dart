import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// Page d'historique des trajets rangés par jour
class JourneyHistoryPage extends StatefulWidget {
  const JourneyHistoryPage({super.key});

  @override
  State<JourneyHistoryPage> createState() => _JourneyHistoryPageState();
}

class _JourneyHistoryPageState extends State<JourneyHistoryPage> {
  // Données simulées de trajets groupés par jour
  final Map<String, List<Journey>> _journeysByDay = {
    'Aujourd\'hui': [
      Journey(
        id: '1',
        startTime: DateTime.now().subtract(const Duration(hours: 2)),
        endTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
        startLocation: 'Dom<PERSON>le, Cocody',
        endLocation: 'Plateau, Centre-ville',
        distance: 8.5,
        duration: const Duration(minutes: 30),
        averageSpeed: 17.0,
        maxSpeed: 25.0,
        route: [
          const LatLng(5.3444, -3.9738), // Cocody
          const LatLng(5.3350, -4.0100),
          const LatLng(5.3250, -4.0200),
          const LatLng(5.3196, -4.0255), // Plateau
        ],
        stops: [
          JourneyStop(
            location: 'Pharmacie du Plateau',
            coordinates: const LatLng(5.3220, -4.0230),
            arrivalTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
            departureTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 40)),
            duration: const Duration(minutes: 5),
          ),
        ],
      ),
      Journey(
        id: '2',
        startTime: DateTime.now().subtract(const Duration(hours: 5)),
        endTime: DateTime.now().subtract(const Duration(hours: 4, minutes: 15)),
        startLocation: 'Plateau, Centre-ville',
        endLocation: 'Marché de Treichville',
        distance: 4.2,
        duration: const Duration(minutes: 45),
        averageSpeed: 5.6,
        maxSpeed: 12.0,
        route: [
          const LatLng(5.3196, -4.0255), // Plateau
          const LatLng(5.3100, -4.0200),
          const LatLng(5.2900, -4.0150),
          const LatLng(5.2767, -4.0081), // Treichville
        ],
        stops: [],
      ),
    ],
    'Hier': [
      Journey(
        id: '3',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
        endTime: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
        startLocation: 'Domicile, Cocody',
        endLocation: 'Hôpital Général',
        distance: 12.3,
        duration: const Duration(hours: 1),
        averageSpeed: 12.3,
        maxSpeed: 30.0,
        route: [
          const LatLng(5.3444, -3.9738),
          const LatLng(5.3300, -4.0000),
          const LatLng(5.3200, -4.0100),
          const LatLng(5.3100, -4.0200),
        ],
        stops: [],
      ),
    ],
    'Il y a 2 jours': [
      Journey(
        id: '4',
        startTime: DateTime.now().subtract(const Duration(days: 2, hours: 4)),
        endTime: DateTime.now().subtract(const Duration(days: 2, hours: 3, minutes: 20)),
        startLocation: 'Domicile, Cocody',
        endLocation: 'Université FHB',
        distance: 15.7,
        duration: const Duration(minutes: 40),
        averageSpeed: 23.6,
        maxSpeed: 35.0,
        route: [
          const LatLng(5.3444, -3.9738),
          const LatLng(5.3600, -3.9500),
          const LatLng(5.3800, -3.9200),
          const LatLng(5.4000, -3.9000),
        ],
        stops: [],
      ),
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Panneau de statistiques
        _buildStatsPanel(),
        
        // Liste des trajets par jour
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _journeysByDay.keys.length,
            itemBuilder: (context, index) {
              final day = _journeysByDay.keys.elementAt(index);
              final journeys = _journeysByDay[day]!;
              return _buildDaySection(day, journeys);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsPanel() {
    // Calculer les statistiques globales
    int totalJourneys = 0;
    double totalDistance = 0;
    Duration totalDuration = Duration.zero;
    
    for (final journeys in _journeysByDay.values) {
      totalJourneys += journeys.length;
      for (final journey in journeys) {
        totalDistance += journey.distance;
        totalDuration += journey.duration;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatItem('Trajets', totalJourneys.toString(), Icons.route, Colors.blue),
          const SizedBox(width: 16),
          _buildStatItem('Distance', '${totalDistance.toStringAsFixed(1)} km', Icons.straighten, Colors.green),
          const SizedBox(width: 16),
          _buildStatItem('Temps', '${totalDuration.inHours}h ${totalDuration.inMinutes % 60}min', Icons.access_time, Colors.orange),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDaySection(String day, List<Journey> journeys) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête du jour
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Text(
                day,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFF7900),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7900).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${journeys.length} trajet${journeys.length > 1 ? 's' : ''}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFFFF7900),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Liste des trajets du jour
        ...journeys.map((journey) => _buildJourneyCard(journey)),
        
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildJourneyCard(Journey journey) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showJourneyDetails(journey),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heure et durée
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${_formatTime(journey.startTime)} - ${_formatTime(journey.endTime!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${journey.duration.inMinutes} min',
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Trajet
              Row(
                children: [
                  Column(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      Container(
                        width: 2,
                        height: 20,
                        color: Colors.grey[300],
                      ),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          journey.startLocation,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          journey.endLocation,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Statistiques du trajet
              Row(
                children: [
                  _buildJourneyStatItem(Icons.straighten, '${journey.distance.toStringAsFixed(1)} km'),
                  const SizedBox(width: 16),
                  _buildJourneyStatItem(Icons.speed, '${journey.averageSpeed.toStringAsFixed(1)} km/h'),
                  if (journey.stops.isNotEmpty) ...[
                    const SizedBox(width: 16),
                    _buildJourneyStatItem(Icons.stop_circle, '${journey.stops.length} arrêt${journey.stops.length > 1 ? 's' : ''}'),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJourneyStatItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showJourneyDetails(Journey journey) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Handle
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // En-tête
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.route, color: Color(0xFFFF7900)),
                      const SizedBox(width: 8),
                      const Text(
                        'Détails du trajet',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                // Contenu scrollable
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      children: [
                        // Carte du trajet
                        Container(
                          height: 200,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: _buildJourneyMap(journey),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Détails du trajet
                        _buildJourneyDetailsContent(journey),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildJourneyMap(Journey journey) {
    return FlutterMap(
      options: MapOptions(
        initialCenter: journey.route.first,
        initialZoom: 13.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.example.canne_connectee',
        ),
        PolylineLayer(
          polylines: [
            Polyline(
              points: journey.route,
              strokeWidth: 4.0,
              color: const Color(0xFFFF7900),
            ),
          ],
        ),
        MarkerLayer(
          markers: [
            // Marqueur de départ
            Marker(
              point: journey.route.first,
              width: 30,
              height: 30,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
            // Marqueur d'arrivée
            Marker(
              point: journey.route.last,
              width: 30,
              height: 30,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.stop,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
            // Marqueurs des arrêts
            ...journey.stops.map((stop) => Marker(
              point: stop.coordinates,
              width: 20,
              height: 20,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.orange,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.pause,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildJourneyDetailsContent(Journey journey) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Informations générales
          _buildDetailSection(
            'Informations générales',
            [
              _buildDetailRow('Heure de départ', _formatTime(journey.startTime)),
              _buildDetailRow('Heure d\'arrivée', _formatTime(journey.endTime!)),
              _buildDetailRow('Durée totale', '${journey.duration.inMinutes} minutes'),
              _buildDetailRow('Distance parcourue', '${journey.distance.toStringAsFixed(1)} km'),
              _buildDetailRow('Vitesse moyenne', '${journey.averageSpeed.toStringAsFixed(1)} km/h'),
              _buildDetailRow('Vitesse maximale', '${journey.maxSpeed.toStringAsFixed(1)} km/h'),
            ],
          ),

          const SizedBox(height: 20),

          // Itinéraire
          _buildDetailSection(
            'Itinéraire',
            [
              _buildLocationRow('Départ', journey.startLocation, Colors.green),
              if (journey.stops.isNotEmpty) ...[
                const SizedBox(height: 8),
                ...journey.stops.map((stop) => _buildStopRow(stop)),
              ],
              const SizedBox(height: 8),
              _buildLocationRow('Arrivée', journey.endLocation, Colors.red),
            ],
          ),

          if (journey.stops.isNotEmpty) ...[
            const SizedBox(height: 20),
            _buildDetailSection(
              'Arrêts (${journey.stops.length})',
              journey.stops.map((stop) => _buildStopDetails(stop)).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFFFF7900),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationRow(String type, String location, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$type: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            location,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStopRow(JourneyStop stop) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.orange,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              stop.location,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStopDetails(JourneyStop stop) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            stop.location,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Arrivée: ${_formatTime(stop.arrivalTime)} - Départ: ${_formatTime(stop.departureTime)}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Text(
            'Durée d\'arrêt: ${stop.duration.inMinutes} minutes',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

// Modèles de données
class Journey {
  final String id;
  final DateTime startTime;
  final DateTime? endTime;
  final String startLocation;
  final String endLocation;
  final double distance;
  final Duration duration;
  final double averageSpeed;
  final double maxSpeed;
  final List<LatLng> route;
  final List<JourneyStop> stops;

  Journey({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.startLocation,
    required this.endLocation,
    required this.distance,
    required this.duration,
    required this.averageSpeed,
    required this.maxSpeed,
    required this.route,
    required this.stops,
  });
}

class JourneyStop {
  final String location;
  final LatLng coordinates;
  final DateTime arrivalTime;
  final DateTime departureTime;
  final Duration duration;

  JourneyStop({
    required this.location,
    required this.coordinates,
    required this.arrivalTime,
    required this.departureTime,
    required this.duration,
  });
}

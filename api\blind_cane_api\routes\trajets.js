const express = require('express');
const { body, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/trajets
 * @desc Récupère la liste des trajets
 */
router.get('/', authMiddleware, [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('status').optional().isIn(['en_cours', 'termine', 'annule']).withMessage('Statut invalide'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const { user_id, status, limit = 50, offset = 0 } = req.query;

    // Simulation de données de trajets
    const trajets = [
      {
        id: 1,
        user_id: 1,
        nom_trajet: 'Domicile - Bureau',
        point_depart: {
          nom: 'Domicile',
          latitude: 48.8566,
          longitude: 2.3522,
          adresse: '123 Rue de la Paix, Paris'
        },
        point_arrivee: {
          nom: 'Bureau',
          latitude: 48.8606,
          longitude: 2.3376,
          adresse: '456 Avenue des Champs-Élysées, Paris'
        },
        distance_km: 2.5,
        duree_estimee: 1800, // 30 minutes en secondes
        duree_reelle: 2100, // 35 minutes
        status: 'termine',
        date_debut: new Date(Date.now() - 7200000).toISOString(), // il y a 2h
        date_fin: new Date(Date.now() - 5100000).toISOString(), // il y a 1h25
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        user_id: 1,
        nom_trajet: 'Pharmacie',
        point_depart: {
          nom: 'Domicile',
          latitude: 48.8566,
          longitude: 2.3522,
          adresse: '123 Rue de la Paix, Paris'
        },
        point_arrivee: {
          nom: 'Pharmacie Centrale',
          latitude: 48.8580,
          longitude: 2.3540,
          adresse: '789 Boulevard Saint-Germain, Paris'
        },
        distance_km: 0.8,
        duree_estimee: 600, // 10 minutes
        duree_reelle: null,
        status: 'en_cours',
        date_debut: new Date(Date.now() - 300000).toISOString(), // il y a 5 min
        date_fin: null,
        created_at: new Date().toISOString()
      }
    ];

    let filteredTrajets = trajets;
    
    if (user_id) {
      filteredTrajets = filteredTrajets.filter(trajet => trajet.user_id === parseInt(user_id));
    }

    if (status) {
      filteredTrajets = filteredTrajets.filter(trajet => trajet.status === status);
    }

    const paginatedTrajets = filteredTrajets.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

    res.json({
      trajets: paginatedTrajets,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: filteredTrajets.length
      }
    });

  } catch (error) {
    console.error('Erreur récupération trajets:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des trajets',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/trajets
 * @desc Crée un nouveau trajet
 */
router.post('/', authMiddleware, [
  body('nom_trajet')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Le nom du trajet doit contenir entre 2 et 200 caractères'),
  
  body('point_depart.nom')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Le nom du point de départ est requis'),
  
  body('point_depart.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude de départ invalide'),
  
  body('point_depart.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude de départ invalide'),
  
  body('point_arrivee.nom')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Le nom du point d\'arrivée est requis'),
  
  body('point_arrivee.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude d\'arrivée invalide'),
  
  body('point_arrivee.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude d\'arrivée invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { nom_trajet, point_depart, point_arrivee } = req.body;

    // Calcul approximatif de la distance (formule haversine simplifiée)
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
      const R = 6371; // Rayon de la Terre en km
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    };

    const distance = calculateDistance(
      point_depart.latitude,
      point_depart.longitude,
      point_arrivee.latitude,
      point_arrivee.longitude
    );

    // Estimation de durée (vitesse moyenne de marche : 5 km/h)
    const duree_estimee = Math.round((distance / 5) * 3600); // en secondes

    const newTrajet = {
      id: Date.now(),
      user_id: req.user?.userId || 1,
      nom_trajet,
      point_depart,
      point_arrivee,
      distance_km: Math.round(distance * 100) / 100, // arrondi à 2 décimales
      duree_estimee,
      duree_reelle: null,
      status: 'en_cours',
      date_debut: new Date().toISOString(),
      date_fin: null,
      created_at: new Date().toISOString()
    };

    res.status(201).json({
      message: 'Trajet créé avec succès',
      trajet: newTrajet
    });

  } catch (error) {
    console.error('Erreur création trajet:', error);
    res.status(500).json({
      error: 'Erreur lors de la création du trajet',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/trajets/:id
 * @desc Récupère un trajet spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const trajetId = parseInt(req.params.id);
    
    if (isNaN(trajetId)) {
      return res.status(400).json({
        error: 'ID trajet invalide'
      });
    }

    // Simulation de récupération de trajet
    const trajet = {
      id: trajetId,
      user_id: 1,
      nom_trajet: 'Domicile - Bureau',
      point_depart: {
        nom: 'Domicile',
        latitude: 48.8566,
        longitude: 2.3522,
        adresse: '123 Rue de la Paix, Paris'
      },
      point_arrivee: {
        nom: 'Bureau',
        latitude: 48.8606,
        longitude: 2.3376,
        adresse: '456 Avenue des Champs-Élysées, Paris'
      },
      distance_km: 2.5,
      duree_estimee: 1800,
      duree_reelle: 2100,
      status: 'termine',
      date_debut: new Date(Date.now() - 7200000).toISOString(),
      date_fin: new Date(Date.now() - 5100000).toISOString(),
      created_at: new Date().toISOString()
    };

    res.json({
      trajet
    });

  } catch (error) {
    console.error('Erreur récupération trajet:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération du trajet',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/trajets/:id/finish
 * @desc Termine un trajet en cours
 */
router.put('/:id/finish', authMiddleware, async (req, res) => {
  try {
    const trajetId = parseInt(req.params.id);
    
    if (isNaN(trajetId)) {
      return res.status(400).json({
        error: 'ID trajet invalide'
      });
    }

    // Simulation de fin de trajet
    const finishedTrajet = {
      id: trajetId,
      status: 'termine',
      date_fin: new Date().toISOString(),
      duree_reelle: 2100, // Exemple : 35 minutes
      updated_at: new Date().toISOString()
    };

    res.json({
      message: 'Trajet terminé avec succès',
      trajet: finishedTrajet
    });

  } catch (error) {
    console.error('Erreur fin trajet:', error);
    res.status(500).json({
      error: 'Erreur lors de la finalisation du trajet',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/trajets/:id/cancel
 * @desc Annule un trajet en cours
 */
router.put('/:id/cancel', authMiddleware, async (req, res) => {
  try {
    const trajetId = parseInt(req.params.id);
    
    if (isNaN(trajetId)) {
      return res.status(400).json({
        error: 'ID trajet invalide'
      });
    }

    // Simulation d'annulation de trajet
    const cancelledTrajet = {
      id: trajetId,
      status: 'annule',
      date_fin: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.json({
      message: 'Trajet annulé avec succès',
      trajet: cancelledTrajet
    });

  } catch (error) {
    console.error('Erreur annulation trajet:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'annulation du trajet',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/trajets/:id
 * @desc Supprime un trajet
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const trajetId = parseInt(req.params.id);
    
    if (isNaN(trajetId)) {
      return res.status(400).json({
        error: 'ID trajet invalide'
      });
    }

    res.json({
      message: 'Trajet supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression trajet:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du trajet',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

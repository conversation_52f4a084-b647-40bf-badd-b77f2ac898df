import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// Page de suivi en temps réel de la position de l'aveugle
class RealTimeTrackingPage extends StatefulWidget {
  const RealTimeTrackingPage({super.key});

  @override
  State<RealTimeTrackingPage> createState() => _RealTimeTrackingPageState();
}

class _RealTimeTrackingPageState extends State<RealTimeTrackingPage> {
  final MapController _mapController = MapController();
  
  // Position simulée (à remplacer par les vraies données)
  LatLng _currentPosition = const LatLng(5.3196, -4.0255); // Plateau, Abidjan
  bool _isTracking = true;
  String _lastUpdate = 'Il y a 2 minutes';
  double _accuracy = 5.2;
  double _speed = 2.5;
  String _address = 'Plateau, Abidjan, Côte d\'Ivoire';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Panneau d'informations
          _buildInfoPanel(),
          
          // Carte
          Expanded(
            child: _buildMap(),
          ),
          
          // Panneau de contrôles
          _buildControlPanel(),
        ],
      ),
    );
  }

  Widget _buildInfoPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _isTracking ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _isTracking ? Icons.gps_fixed : Icons.gps_off,
                  color: _isTracking ? Colors.green : Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isTracking ? 'Suivi actif' : 'Suivi interrompu',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _isTracking ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      'Dernière mise à jour: $_lastUpdate',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _refreshLocation,
                icon: const Icon(Icons.refresh),
                tooltip: 'Actualiser',
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _address,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildInfoItem('Précision', '${_accuracy.toStringAsFixed(1)}m', Icons.gps_fixed),
                    const SizedBox(width: 16),
                    _buildInfoItem('Vitesse', '${_speed.toStringAsFixed(1)} km/h', Icons.speed),
                    const SizedBox(width: 16),
                    _buildInfoItem('Coordonnées', '${_currentPosition.latitude.toStringAsFixed(4)}°, ${_currentPosition.longitude.toStringAsFixed(4)}°', Icons.place),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Expanded(
      child: Row(
        children: [
          Icon(icon, size: 14, color: Colors.grey),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: _currentPosition,
        initialZoom: 16.0,
        minZoom: 10.0,
        maxZoom: 18.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.example.canne_connectee',
        ),
        MarkerLayer(
          markers: [
            Marker(
              point: _currentPosition,
              width: 60,
              height: 60,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.person_pin_circle,
                    color: Colors.blue,
                    size: 40,
                  ),
                ),
              ),
            ),
          ],
        ),
        CircleLayer(
          circles: [
            CircleMarker(
              point: _currentPosition,
              radius: _accuracy,
              useRadiusInMeter: true,
              color: Colors.blue.withOpacity(0.1),
              borderColor: Colors.blue.withOpacity(0.3),
              borderStrokeWidth: 2,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _centerOnPosition,
              icon: const Icon(Icons.my_location),
              label: const Text('Centrer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _showTrackingOptions,
              icon: const Icon(Icons.settings),
              label: const Text('Options'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _shareLocation,
              icon: const Icon(Icons.share),
              label: const Text('Partager'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _refreshLocation() {
    setState(() {
      _lastUpdate = 'Actualisation...';
    });
    
    // Simuler une actualisation
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _lastUpdate = 'À l\'instant';
          // Simuler un léger changement de position
          _currentPosition = LatLng(
            _currentPosition.latitude + (0.0001 * (DateTime.now().millisecond % 10 - 5)),
            _currentPosition.longitude + (0.0001 * (DateTime.now().millisecond % 10 - 5)),
          );
        });
        _mapController.move(_currentPosition, 16.0);
      }
    });
  }

  void _centerOnPosition() {
    _mapController.move(_currentPosition, 16.0);
  }

  void _showTrackingOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Options de suivi',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('Alertes de mouvement'),
              subtitle: const Text('Recevoir des notifications'),
              trailing: Switch(
                value: true,
                onChanged: (value) {},
              ),
            ),
            ListTile(
              leading: const Icon(Icons.timer),
              title: const Text('Fréquence de mise à jour'),
              subtitle: const Text('Toutes les 30 secondes'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {},
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Historique des positions'),
              subtitle: const Text('Voir les positions passées'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  void _shareLocation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Partager la position'),
        content: const Text('Voulez-vous partager la position actuelle ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Position partagée avec succès'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Partager'),
          ),
        ],
      ),
    );
  }
}

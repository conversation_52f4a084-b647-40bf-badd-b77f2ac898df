// Serveur simple pour tester la configuration de base
console.log('🧪 Test du serveur simple...');

try {
  const express = require('express');
  const cors = require('cors');
  require('dotenv').config();

  const app = express();
  const PORT = process.env.PORT || 3001;

  // Middleware de base
  app.use(cors());
  app.use(express.json());

  // Route de test
  app.get('/', (req, res) => {
    res.json({
      message: '✅ Serveur API Canne Connectée - Test réussi !',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      status: 'OK'
    });
  });

  // Route de santé
  app.get('/health', (req, res) => {
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Route de test pour les alertes
  app.get('/api/alertes', (req, res) => {
    res.json({
      message: 'Route alertes fonctionnelle',
      alertes: [
        {
          id: 1,
          type: 'sos',
          message: 'Test alerte SOS',
          date: new Date().toISOString()
        }
      ]
    });
  });

  // Gestion d'erreurs simple
  app.use((err, req, res, next) => {
    console.error('❌ Erreur:', err);
    res.status(500).json({
      error: 'Erreur serveur',
      message: err.message
    });
  });

  // Route 404
  app.use('*', (req, res) => {
    res.status(404).json({
      error: 'Route non trouvée',
      path: req.originalUrl
    });
  });

  // Démarrer le serveur
  app.listen(PORT, () => {
    console.log('🌟 ================================');
    console.log('🎯 SERVEUR SIMPLE DÉMARRÉ');
    console.log('🌟 ================================');
    console.log(`🌐 URL: http://localhost:${PORT}`);
    console.log(`📊 Environnement: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📋 Routes disponibles:`);
    console.log(`   • GET  / - Page d'accueil`);
    console.log(`   • GET  /health - État du serveur`);
    console.log(`   • GET  /api/alertes - Test alertes`);
    console.log('🌟 ================================\n');
  });

} catch (error) {
  console.error('❌ Erreur lors du démarrage du serveur simple:', error);
  console.error('📍 Stack trace:', error.stack);
  process.exit(1);
}

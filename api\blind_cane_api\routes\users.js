const express = require('express');
const { body, query, validationResult } = require('express-validator');
const User = require('../models/User');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/users
 * @desc Récupère la liste des utilisateurs
 */
router.get('/', authMiddleware, [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const limit = parseInt(req.query.limit) || 20;
    const offset = parseInt(req.query.offset) || 0;

    const users = await User.findAll({ limit, offset });
    
    res.json({
      users: users.map(user => user.toJSON()),
      pagination: {
        limit,
        offset,
        total: users.length
      }
    });

  } catch (error) {
    console.error('Erreur récupération utilisateurs:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des utilisateurs',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/users/:id
 * @desc Récupère un utilisateur spécifique
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    
    if (isNaN(userId)) {
      return res.status(400).json({
        error: 'ID utilisateur invalide'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'Utilisateur non trouvé'
      });
    }

    res.json({
      user: user.toJSON()
    });

  } catch (error) {
    console.error('Erreur récupération utilisateur:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération de l\'utilisateur',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route PUT /api/users/:id
 * @desc Met à jour un utilisateur
 */
router.put('/:id', authMiddleware, [
  body('nom_utilisateur')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('prenom_utilisateur')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le prénom doit contenir entre 2 et 100 caractères'),
  
  body('email_utilisateur')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  
  body('contact_utilisateur')
    .optional()
    .isMobilePhone('fr-FR')
    .withMessage('Numéro de téléphone invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const userId = parseInt(req.params.id);
    
    if (isNaN(userId)) {
      return res.status(400).json({
        error: 'ID utilisateur invalide'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'Utilisateur non trouvé'
      });
    }

    // Mettre à jour les champs fournis
    const updateData = {};
    const allowedFields = ['nom_utilisateur', 'prenom_utilisateur', 'email_utilisateur', 'contact_utilisateur', 'adresse_utilisateur'];
    
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const updatedUser = await user.update(updateData);

    res.json({
      message: 'Utilisateur mis à jour avec succès',
      user: updatedUser.toJSON()
    });

  } catch (error) {
    console.error('Erreur mise à jour utilisateur:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour de l\'utilisateur',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route DELETE /api/users/:id
 * @desc Supprime un utilisateur
 */
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    
    if (isNaN(userId)) {
      return res.status(400).json({
        error: 'ID utilisateur invalide'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'Utilisateur non trouvé'
      });
    }

    await user.delete();

    res.json({
      message: 'Utilisateur supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression utilisateur:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression de l\'utilisateur',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

module.exports = router;

{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.59", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "battery_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/battery_plus-5.0.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "battery_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/battery_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "camera", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera-0.11.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "camera_android_camerax", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_android_camerax-0.6.18+3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "camera_avfoundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.20", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "camera_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "camera_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_web-0.3.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cross_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_earcut", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_earcut-1.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dbus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "event_bus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/event_bus-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "firebase_auth", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.7.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///C:/src/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_map", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_map-8.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///C:/src/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_tts", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_tts-4.2.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/src/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "font_awesome_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/font_awesome_flutter-10.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "geocoding", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding-4.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "geocoding_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_android-4.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "geocoding_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_ios-3.0.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "geocoding_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "geolocator", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-13.0.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-4.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "google_identity_services_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.9.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "latlong2", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/latlong2-0.9.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "lists", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lists-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "logger", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logger-2.5.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mgrs_dart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mgrs_dart-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mqtt_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mqtt_client-10.8.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "nested", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pedantic", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pedantic-1.11.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "permission_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-12.0.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-13.0.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "polylabel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/polylabel-1.0.1", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "proj4dart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/proj4dart-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "record", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record-5.2.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "record_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_android-1.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "record_darwin", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_darwin-1.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "record_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_linux-0.7.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "record_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_platform_interface-1.2.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "record_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_web-1.1.8", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "record_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/record_windows-1.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "sensors_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sensors_plus-4.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sensors_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sensors_plus_platform_interface-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sign_in_with_apple-6.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///C:/src/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "speech_to_text", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/speech_to_text-7.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "speech_to_text_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sprintf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "unicode", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/unicode-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "upower", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/upower-0.7.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wkt_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wkt_parser-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "canne_connectee", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-07-27T02:11:03.788411Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///C:/src/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}